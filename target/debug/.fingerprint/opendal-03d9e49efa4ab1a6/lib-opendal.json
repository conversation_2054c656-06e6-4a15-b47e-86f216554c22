{"rustc": 6560579996391851404, "features": "[\"default\", \"rustls\", \"services-azblob\", \"services-azdls\", \"services-azfile\", \"services-cos\", \"services-fs\", \"services-gcs\", \"services-ghac\", \"services-http\", \"services-ipmfs\", \"services-memory\", \"services-obs\", \"services-oss\", \"services-s3\", \"services-webdav\", \"services-webhdfs\"]", "declared_features": "[\"default\", \"hdfs-native\", \"internal-http-cookies\", \"internal-path-cache\", \"layers-all\", \"layers-async-backtrace\", \"layers-await-tree\", \"layers-chaos\", \"layers-madsim\", \"layers-metrics\", \"layers-minitrace\", \"layers-otel-trace\", \"layers-prometheus\", \"layers-prometheus-client\", \"layers-throttle\", \"layers-tracing\", \"native-tls\", \"native-tls-vendored\", \"rustls\", \"services-alluxio\", \"services-atomicserver\", \"services-azblob\", \"services-azdls\", \"services-azfile\", \"services-b2\", \"services-cacache\", \"services-chainsafe\", \"services-cloudflare-kv\", \"services-cos\", \"services-d1\", \"services-dashmap\", \"services-dbfs\", \"services-dropbox\", \"services-etcd\", \"services-foundationdb\", \"services-fs\", \"services-ftp\", \"services-gcs\", \"services-gdrive\", \"services-ghac\", \"services-gridfs\", \"services-hdfs\", \"services-hdfs-native\", \"services-http\", \"services-huggingface\", \"services-icloud\", \"services-ipfs\", \"services-ipmfs\", \"services-koofr\", \"services-libsql\", \"services-memcached\", \"services-memory\", \"services-mini-moka\", \"services-moka\", \"services-mongodb\", \"services-mysql\", \"services-obs\", \"services-onedrive\", \"services-oss\", \"services-pcloud\", \"services-persy\", \"services-postgresql\", \"services-redb\", \"services-redis\", \"services-redis-native-tls\", \"services-rocksdb\", \"services-s3\", \"services-seafile\", \"services-sftp\", \"services-sled\", \"services-sqlite\", \"services-supabase\", \"services-swift\", \"services-tikv\", \"services-upyun\", \"services-vercel-artifacts\", \"services-wasabi\", \"services-webdav\", \"services-webhdfs\", \"services-yandex-disk\", \"tests\", \"tikv-client\", \"trust-dns\"]", "target": 10444371102455581833, "profile": 5347358027863023418, "path": 15819776039727882531, "deps": [[40386456601120721, "percent_encoding", false, 2474654511784584099], [2706460456408817945, "futures", false, 2017779666131399731], [3722963349756955755, "once_cell", false, 7515198253589974185], [4045648576830848642, "backon", false, 16911332324541947773], [4405182208873388884, "http", false, 10861615716676178807], [5631097499276217816, "flagset", false, 7062947406950969582], [5986029879202738730, "log", false, 9300570882141189259], [7051825882133757896, "md5", false, 15906521746837188282], [7244058819997729774, "reqwest", false, 6104077676121141045], [8319709847752024821, "uuid", false, 9765508526265252409], [9538054652646069845, "tokio", false, 353258954800503627], [9689903380558560274, "serde", false, 18265033257570559074], [9857275760291862238, "sha2", false, 3930233802144602266], [9897246384292347999, "chrono", false, 1032213656128219476], [10985752566541544495, "quick_xml", false, 5817007674079651516], [11946729385090170470, "async_trait", false, 3230428666755426466], [13625485746686963219, "anyhow", false, 1239931696919690419], [14884195152848921884, "reqsign", false, 6431359971155803723], [15367738274754116744, "serde_json", false, 10553664108743005500], [16066129441945555748, "bytes", false, 13079279578986384596], [18066890886671768183, "base64", false, 132961652928832079]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/opendal-03d9e49efa4ab1a6/dep-lib-opendal", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}