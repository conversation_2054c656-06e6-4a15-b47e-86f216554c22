{"rustc": 6560579996391851404, "features": "[]", "declared_features": "[]", "target": 12433420480382756260, "profile": 2330448797067240312, "path": 8234822654938402816, "deps": [[530211389790465181, "hex", false, 15327423130590005232], [1441306149310335789, "tempfile", false, 17005883417082440183], [1582828171158827377, "tauri_plugin_shell", false, 2426131079097536305], [3935545708480822364, "tauri_plugin_opener", false, 12872735073620624969], [4045648576830848642, "backon", false, 12812480598318786704], [8256202458064874477, "dirs", false, 1657723661557744777], [8319709847752024821, "uuid", false, 17570888467612569951], [8606274917505247608, "tracing", false, 2010047090811671594], [9538054652646069845, "tokio", false, 507647344404838273], [9689903380558560274, "serde", false, 14035322482825346197], [9857275760291862238, "sha2", false, 8985867638099803347], [9897246384292347999, "chrono", false, 12730471910881604416], [10632374999838431203, "sqlx", false, 8677352238225678508], [10755362358622467486, "tauri", false, 17759112858836517999], [12024781371499446814, "infer", false, 12313461954190827856], [12651179010648504719, "clippy_lib", false, 11604014713810996832], [12651179010648504719, "build_script_build", false, 12811016406664780704], [13625485746686963219, "anyhow", false, 3777144871397418491], [14362781625892631394, "lz4_flex", false, 3060694328248461417], [15367738274754116744, "serde_json", false, 3949303371299155927], [16127819450160124995, "opendal", false, 15777388698799922009], [16230660778393187092, "tracing_subscriber", false, 10450075090632228799], [17254983958339362062, "clipboard_rs", false, 5779961149310501826], [18066890886671768183, "base64", false, 3171077618145496841]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/clippy-8710acade299a807/dep-example-hybrid_storage_demo", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}