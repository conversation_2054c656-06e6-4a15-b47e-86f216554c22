{"rustc": 6560579996391851404, "features": "[\"_rt-tokio\", \"any\", \"chrono\", \"default\", \"json\", \"macros\", \"migrate\", \"runtime-tokio\", \"runtime-tokio-rustls\", \"sqlite\", \"sqlx-macros\", \"sqlx-sqlite\", \"tls-rustls\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_unstable-all-types\", \"all-databases\", \"any\", \"bigdecimal\", \"bit-vec\", \"chrono\", \"default\", \"ipnetwork\", \"json\", \"mac_address\", \"macros\", \"migrate\", \"mysql\", \"postgres\", \"regexp\", \"runtime-async-std\", \"runtime-async-std-native-tls\", \"runtime-async-std-rustls\", \"runtime-tokio\", \"runtime-tokio-native-tls\", \"runtime-tokio-rustls\", \"rust_decimal\", \"sqlite\", \"sqlx-macros\", \"sqlx-mysql\", \"sqlx-postgres\", \"sqlx-sqlite\", \"time\", \"tls-native-tls\", \"tls-none\", \"tls-rustls\", \"uuid\"]", "target": 3003836824758849296, "profile": 5347358027863023418, "path": 3472173030682388685, "deps": [[228475551920078470, "sqlx_macros", false, 18419060888075698564], [996810380461694889, "sqlx_core", false, 2839650161457017779], [11838249260056359578, "sqlx_sqlite", false, 13074022605467175255]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/sqlx-5d95154933aa86cd/dep-lib-sqlx", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}