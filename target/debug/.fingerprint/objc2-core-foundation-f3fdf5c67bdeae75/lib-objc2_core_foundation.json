{"rustc": 6560579996391851404, "features": "[\"CFArray\", \"CFCGTypes\", \"CFData\", \"CFDictionary\", \"CFRunLoop\", \"alloc\", \"bitflags\", \"objc2\", \"std\"]", "declared_features": "[\"CFArray\", \"CFAttributedString\", \"CFAvailability\", \"CFBag\", \"CFBase\", \"CFBinaryHeap\", \"CFBitVector\", \"CFBundle\", \"CFByteOrder\", \"CFCGTypes\", \"CFCalendar\", \"CFCharacterSet\", \"CFData\", \"CFDate\", \"CFDateFormatter\", \"CFDictionary\", \"CFError\", \"CFFileDescriptor\", \"CFFileSecurity\", \"CFLocale\", \"CFMachPort\", \"CFMessagePort\", \"CFNotificationCenter\", \"CFNumber\", \"CFNumberFormatter\", \"CFPlugIn\", \"CFPlugInCOM\", \"CFPreferences\", \"CFPropertyList\", \"CFRunLoop\", \"CFSet\", \"CFSocket\", \"CFStream\", \"CFString\", \"CFStringEncodingExt\", \"CFStringTokenizer\", \"CFTimeZone\", \"CFTree\", \"CFURL\", \"CFURLAccess\", \"CFURLEnumerator\", \"CFUUID\", \"CFUserNotification\", \"CFUtilities\", \"CFXMLNode\", \"CFXMLParser\", \"alloc\", \"bitflags\", \"block2\", \"default\", \"dispatch2\", \"libc\", \"objc2\", \"std\", \"unstable-coerce-pointee\"]", "target": 9250166696766853962, "profile": 8196097686603091492, "path": 15238672537460998730, "deps": [[1386409696764982933, "objc2", false, 8313004703579478711], [7896293946984509699, "bitflags", false, 3835871275300008516]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/objc2-core-foundation-f3fdf5c67bdeae75/dep-lib-objc2_core_foundation", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}