{"rustc": 6560579996391851404, "features": "[\"CKContainer\", \"CKRecord\", \"CKShare\", \"CKShareMetadata\", \"bitflags\"]", "declared_features": "[\"CKAcceptSharesOperation\", \"CKAllowedSharingOptions\", \"CKAsset\", \"CKContainer\", \"CKDatabase\", \"CKDatabaseOperation\", \"CKDefines\", \"CKDiscoverAllUserIdentitiesOperation\", \"CKDiscoverUserIdentitiesOperation\", \"CKError\", \"CKFetchDatabaseChangesOperation\", \"CKFetchNotificationChangesOperation\", \"CKFetchRecordChangesOperation\", \"CKFetchRecordZoneChangesOperation\", \"CKFetchRecordZonesOperation\", \"CKFetchRecordsOperation\", \"CKFetchShareMetadataOperation\", \"CKFetchShareParticipantsOperation\", \"CKFetchSubscriptionsOperation\", \"CKFetchWebAuthTokenOperation\", \"CKLocationSortDescriptor\", \"CKMarkNotificationsReadOperation\", \"CKModifyBadgeOperation\", \"CKModifyRecordZonesOperation\", \"CKModifyRecordsOperation\", \"CKModifySubscriptionsOperation\", \"CKNotification\", \"CKOperation\", \"CKOperationGroup\", \"CKQuery\", \"CKQueryOperation\", \"CKRecord\", \"CKRecordID\", \"CKRecordZone\", \"CKRecordZoneID\", \"CKReference\", \"CKServerChangeToken\", \"CKShare\", \"CKShareMetadata\", \"CKShareParticipant\", \"CKSubscription\", \"CKSyncEngine\", \"CKSyncEngineConfiguration\", \"CKSyncEngineEvent\", \"CKSyncEngineRecordZoneChangeBatch\", \"CKSyncEngineState\", \"CKSystemSharingUIObserver\", \"CKUserIdentity\", \"CKUserIdentityLookupInfo\", \"NSItemProvider_CKSharingSupport\", \"alloc\", \"bitflags\", \"block2\", \"default\", \"objc2-core-location\", \"std\"]", "target": 15282355710264569945, "profile": 8196097686603091492, "path": 11876366340602232162, "deps": [[1386409696764982933, "objc2", false, 8313004703579478711], [7896293946984509699, "bitflags", false, 3835871275300008516], [9859211262912517217, "objc2_foundation", false, 5343901004059671008]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/objc2-cloud-kit-cec7ab40d4ff9bdd/dep-lib-objc2_cloud_kit", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}