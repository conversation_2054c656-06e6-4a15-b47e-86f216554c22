{"rustc": 6560579996391851404, "features": "[\"_rt-tokio\", \"_tls-rustls\", \"any\", \"chrono\", \"crc\", \"default\", \"json\", \"migrate\", \"offline\", \"rustls\", \"rustls-pemfile\", \"serde\", \"serde_json\", \"sha2\", \"tokio\", \"tokio-stream\", \"webpki-roots\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_tls-native-tls\", \"_tls-none\", \"_tls-rustls\", \"any\", \"async-io\", \"async-std\", \"bigdecimal\", \"bit-vec\", \"bstr\", \"chrono\", \"crc\", \"default\", \"digest\", \"encoding_rs\", \"ipnetwork\", \"json\", \"mac_address\", \"migrate\", \"native-tls\", \"num-bigint\", \"offline\", \"regex\", \"rust_decimal\", \"rustls\", \"rustls-pemfile\", \"serde\", \"serde_json\", \"sha1\", \"sha2\", \"time\", \"tokio\", \"tokio-stream\", \"uuid\", \"webpki-roots\"]", "target": 2042750936636613814, "profile": 5347358027863023418, "path": 3885288406002128764, "deps": [[5103565458935487, "futures_io", false, 3780580627768365883], [40386456601120721, "percent_encoding", false, 2474654511784584099], [530211389790465181, "hex", false, 13369340346413766524], [788558663644978524, "crossbeam_queue", false, 17140513444336199349], [966925859616469517, "ahash", false, 16164939131089675840], [1162433738665300155, "crc", false, 9612107416175574562], [1464803193346256239, "event_listener", false, 12361215002752665972], [1811549171721445101, "futures_channel", false, 738546242492721893], [3129130049864710036, "memchr", false, 18418158365258960485], [3150220818285335163, "url", false, 13233234580533072834], [3405817021026194662, "hashlink", false, 5373512711302339097], [3646857438214563691, "futures_intrusive", false, 15005626306916709950], [3666196340704888985, "smallvec", false, 2873008418798999949], [3712811570531045576, "byteorder", false, 9444732249600288311], [3722963349756955755, "once_cell", false, 7515198253589974185], [5986029879202738730, "log", false, 15610553628749370999], [7620660491849607393, "futures_core", false, 8046200181839481027], [8008191657135824715, "thiserror", false, 14995763611910690888], [8606274917505247608, "tracing", false, 16183370000521490526], [9538054652646069845, "tokio", false, 4891591645481723686], [9689903380558560274, "serde", false, 18265033257570559074], [9857275760291862238, "sha2", false, 11791332861809748721], [9897246384292347999, "chrono", false, 8293885901789930823], [10629569228670356391, "futures_util", false, 6395287060282543779], [10862088793507253106, "sqlformat", false, 2335839162576770771], [11295624341523567602, "rustls", false, 1129344563403143969], [12170264697963848012, "either", false, 17334291633927206073], [14483812548788871374, "indexmap", false, 17750916207868685011], [15367738274754116744, "serde_json", false, 12613977156184640993], [16066129441945555748, "bytes", false, 13079279578986384596], [16311359161338405624, "rustls_pemfile", false, 2058974299725807654], [16973251432615581304, "tokio_stream", false, 4685929026493642986], [17106256174509013259, "atoi", false, 10342628123171043038], [17605717126308396068, "paste", false, 1851450414319763739], [17652733826348741533, "webpki_roots", false, 8628639647313677950]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/sqlx-core-bdf8ce0de42220d2/dep-lib-sqlx_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}