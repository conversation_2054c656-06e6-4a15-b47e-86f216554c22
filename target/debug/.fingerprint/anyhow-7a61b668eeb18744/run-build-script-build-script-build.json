{"rustc": 6560579996391851404, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[13625485746686963219, "build_script_build", false, 2813870346989752917]], "local": [{"RerunIfChanged": {"output": "debug/build/anyhow-7a61b668eeb18744/output", "paths": ["src/nightly.rs"]}}, {"RerunIfEnvChanged": {"var": "RUSTC_BOOTSTRAP", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}