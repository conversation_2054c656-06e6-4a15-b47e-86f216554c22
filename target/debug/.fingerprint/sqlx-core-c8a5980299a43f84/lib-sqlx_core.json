{"rustc": 6560579996391851404, "features": "[\"_rt-tokio\", \"_tls-rustls\", \"any\", \"chrono\", \"crc\", \"default\", \"json\", \"migrate\", \"offline\", \"rustls\", \"rustls-pemfile\", \"serde\", \"serde_json\", \"sha2\", \"tokio\", \"tokio-stream\", \"webpki-roots\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_tls-native-tls\", \"_tls-none\", \"_tls-rustls\", \"any\", \"async-io\", \"async-std\", \"bigdecimal\", \"bit-vec\", \"bstr\", \"chrono\", \"crc\", \"default\", \"digest\", \"encoding_rs\", \"ipnetwork\", \"json\", \"mac_address\", \"migrate\", \"native-tls\", \"num-bigint\", \"offline\", \"regex\", \"rust_decimal\", \"rustls\", \"rustls-pemfile\", \"serde\", \"serde_json\", \"sha1\", \"sha2\", \"time\", \"tokio\", \"tokio-stream\", \"uuid\", \"webpki-roots\"]", "target": 2042750936636613814, "profile": 8276155916380437441, "path": 3885288406002128764, "deps": [[5103565458935487, "futures_io", false, 1797018283086566788], [40386456601120721, "percent_encoding", false, 16466772478093754108], [530211389790465181, "hex", false, 15327423130590005232], [788558663644978524, "crossbeam_queue", false, 16648273314380847132], [966925859616469517, "ahash", false, 17408196561806291691], [1162433738665300155, "crc", false, 3390323783534257526], [1464803193346256239, "event_listener", false, 2018195769886119215], [1811549171721445101, "futures_channel", false, 9814466469824529078], [3129130049864710036, "memchr", false, 15703622780043343414], [3150220818285335163, "url", false, 9861320640324392012], [3405817021026194662, "hashlink", false, 14233455690623648984], [3646857438214563691, "futures_intrusive", false, 8345027966424831851], [3666196340704888985, "smallvec", false, 3030394663698996586], [3712811570531045576, "byteorder", false, 11392396475071703026], [3722963349756955755, "once_cell", false, 9562088823409125333], [5986029879202738730, "log", false, 2821614030508259741], [7620660491849607393, "futures_core", false, 9520184049942881440], [8008191657135824715, "thiserror", false, 13767267983148535976], [8606274917505247608, "tracing", false, 2010047090811671594], [9538054652646069845, "tokio", false, 507647344404838273], [9689903380558560274, "serde", false, 14035322482825346197], [9857275760291862238, "sha2", false, 8985867638099803347], [9897246384292347999, "chrono", false, 12730471910881604416], [10629569228670356391, "futures_util", false, 8448264846446034537], [10862088793507253106, "sqlformat", false, 7493590321970369825], [11295624341523567602, "rustls", false, 1833500809724958195], [12170264697963848012, "either", false, 14962812827045685551], [14483812548788871374, "indexmap", false, 6670585893041040373], [15367738274754116744, "serde_json", false, 3949303371299155927], [16066129441945555748, "bytes", false, 6008567741791221099], [16311359161338405624, "rustls_pemfile", false, 17113629808587687425], [16973251432615581304, "tokio_stream", false, 8743941784427956136], [17106256174509013259, "atoi", false, 11891322275460020107], [17605717126308396068, "paste", false, 1851450414319763739], [17652733826348741533, "webpki_roots", false, 6495138851884005198]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/sqlx-core-c8a5980299a43f84/dep-lib-sqlx_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}