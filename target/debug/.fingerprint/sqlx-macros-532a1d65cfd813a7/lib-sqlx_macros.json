{"rustc": 6560579996391851404, "features": "[\"_rt-tokio\", \"_tls-rustls\", \"chrono\", \"default\", \"json\", \"migrate\", \"sqlite\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_tls-native-tls\", \"_tls-rustls\", \"bigdecimal\", \"bit-vec\", \"chrono\", \"default\", \"ipnetwork\", \"json\", \"mac_address\", \"migrate\", \"mysql\", \"postgres\", \"rust_decimal\", \"sqlite\", \"time\", \"uuid\"]", "target": 13494433325021527976, "profile": 3033921117576893, "path": 13000858084093201416, "deps": [[996810380461694889, "sqlx_core", false, 13500139698639651823], [2713742371683562785, "syn", false, 2321626319926927350], [3060637413840920116, "proc_macro2", false, 9227031517908116534], [15733334431800349573, "sqlx_macros_core", false, 878834498211412038], [17990358020177143287, "quote", false, 872382737693232464]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/sqlx-macros-532a1d65cfd813a7/dep-lib-sqlx_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}