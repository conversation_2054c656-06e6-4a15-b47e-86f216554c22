use clippy_lib::hybrid_storage::HybridStorageEngine;
use clippy_lib::storage::{ClipboardItem, FileTypeInfo};
use uuid::Uuid;

fn truncate_string(s: &str, max_len: usize) -> &str {
    if s.len() <= max_len {
        return s;
    }
    
    let mut end = max_len;
    while end > 0 && !s.is_char_boundary(end) {
        end -= 1;
    }
    &s[..end]
}

#[tokio::main]
async fn main() -> anyhow::Result<()> {
    // 初始化日志
    tracing_subscriber::fmt::init();

    // 创建临时存储目录
    let storage_dir = std::env::temp_dir().join("clippy_hybrid_demo");
    let device_id = Uuid::new_v4().to_string();

    println!("🚀 初始化混合存储引擎...");
    let storage_engine = HybridStorageEngine::new(storage_dir.clone(), device_id).await?;

    // 创建一些测试数据
    let test_items = vec![
        ClipboardItem {
            id: Uuid::new_v4().to_string(),
            content: "Hello, World!".to_string(),
            timestamp: chrono::Utc::now().timestamp() as u64,
            item_type: "text".to_string(),
            size: Some(13),
            file_paths: None,
            file_types: None,
        },
        ClipboardItem {
            id: Uuid::new_v4().to_string(),
            content: "这是中文测试内容，包含一些特殊字符 🎉".to_string(),
            timestamp: chrono::Utc::now().timestamp() as u64,
            item_type: "text".to_string(),
            size: Some(45),
            file_paths: None,
            file_types: None,
        },
        ClipboardItem {
            id: Uuid::new_v4().to_string(),
            content: "3 个文件".to_string(),
            timestamp: chrono::Utc::now().timestamp() as u64,
            item_type: "files".to_string(),
            size: Some(1024 * 500), // 500KB
            file_paths: Some(vec![
                "/path/to/document.pdf".to_string(),
                "/path/to/image.png".to_string(),
                "/path/to/code.rs".to_string(),
            ]),
            file_types: Some(vec![
                FileTypeInfo {
                    path: "/path/to/document.pdf".to_string(),
                    file_type: "pdf".to_string(),
                    mime_type: "application/pdf".to_string(),
                    category: "document".to_string(),
                },
                FileTypeInfo {
                    path: "/path/to/image.png".to_string(),
                    file_type: "png".to_string(),
                    mime_type: "image/png".to_string(),
                    category: "image".to_string(),
                },
                FileTypeInfo {
                    path: "/path/to/code.rs".to_string(),
                    file_type: "rs".to_string(),
                    mime_type: "text/rust".to_string(),
                    category: "code".to_string(),
                },
            ]),
        },
        ClipboardItem {
            id: Uuid::new_v4().to_string(),
            content: r#"{"name": "John", "age": 30, "city": "New York"}"#.to_string(),
            timestamp: chrono::Utc::now().timestamp() as u64,
            item_type: "text".to_string(),
            size: Some(47),
            file_paths: None,
            file_types: None,
        },
    ];

    // 插入测试数据
    println!("\n📝 插入测试数据...");
    for (i, item) in test_items.iter().enumerate() {
        println!("  插入项目 {}: {}", i + 1, truncate_string(&item.content, 30));
        storage_engine.insert(item).await?;
    }

    // 获取所有数据
    println!("\n📋 获取所有剪切板项目:");
    let all_items = storage_engine.get_all().await?;
    for (i, item) in all_items.iter().enumerate() {
        println!("  {}: {} ({})", i + 1, truncate_string(&item.content, 50), item.item_type);
    }

    // 分页查询
    println!("\n📄 分页查询 (前2个项目):");
    let paginated_items = storage_engine.get_paginated(2, 0).await?;
    for (i, item) in paginated_items.iter().enumerate() {
        println!("  {}: {}", i + 1, truncate_string(&item.content, 30));
    }

    // 搜索功能
    println!("\n🔍 搜索包含 'Hello' 的项目:");
    let search_results = storage_engine.search("Hello", 10).await?;
    for (i, item) in search_results.iter().enumerate() {
        println!("  {}: {}", i + 1, &item.content);
    }

    // 获取统计信息
    println!("\n📊 存储统计信息:");
    let stats = storage_engine.get_stats().await?;
    println!("  总项目数: {}", stats.total_items);
    println!("  活跃项目数: {}", stats.active_items);
    println!("  已删除项目数: {}", stats.deleted_items);
    println!("  待处理操作数: {}", stats.pending_ops);
    println!("  Oplog 大小: {} bytes", stats.oplog_size);

    // 测试重复内容检测
    println!("\n🔄 测试重复内容检测 (插入相同内容):");
    let duplicate_item = ClipboardItem {
        id: Uuid::new_v4().to_string(),
        content: "Hello, World!".to_string(), // 与第一个项目相同
        timestamp: chrono::Utc::now().timestamp() as u64,
        item_type: "text".to_string(),
        size: Some(13),
        file_paths: None,
        file_types: None,
    };
    storage_engine.insert(&duplicate_item).await?;

    let all_items_after = storage_engine.get_all().await?;
    println!("  插入重复内容后的项目数: {} (应该没有增加)", all_items_after.len());

    // 清理
    println!("\n🧹 执行清理操作:");
    storage_engine.vacuum().await?;

    let final_stats = storage_engine.get_stats().await?;
    println!("  清理后的统计信息:");
    println!("    活跃项目数: {}", final_stats.active_items);
    println!("    Oplog 大小: {} bytes", final_stats.oplog_size);

    println!("\n✅ 混合存储演示完成!");
    println!("📁 存储目录: {}", storage_dir.display());
    println!("  - clipboard.db: SQLite 数据库文件");
    println!("  - oplog.jsonl: 操作日志文件");

    Ok(())
} 