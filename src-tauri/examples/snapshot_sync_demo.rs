use anyhow::Result;
use clippy_lib::hybrid_storage::HybridStorageEngine;
use clippy_lib::hybrid_sync::HybridSyncEngine;
use clippy_lib::sync::{SyncEngine, SyncConfig};
use clippy_lib::storage::ClipboardItem;
use clippy_lib::storage_adapter::{StorageConfig, StorageBackend};
use std::sync::Arc;
use tempfile::TempDir;
use tokio::time::{sleep, Duration};

#[tokio::main]
async fn main() -> Result<()> {
    // 初始化日志
    tracing_subscriber::fmt::init();

    println!("🚀 快照同步机制演示 - 解决新设备历史数据同步问题");
    
    // 创建临时目录用于演示
    let temp_dir = TempDir::new()?;
    let storage_dir = temp_dir.path().to_path_buf();
    let sync_data_dir = temp_dir.path().join("sync_data");
    tokio::fs::create_dir_all(&sync_data_dir).await?;

    println!("📁 使用临时目录: {:?}", storage_dir);

    // 创建存储配置（本地文件系统）
    let storage_config = StorageConfig {
        backend: StorageBackend::FileSystem {
            root_path: sync_data_dir.to_string_lossy().to_string(),
        },
        retry_attempts: 3,
        timeout_seconds: 30,
    };

    // 验证存储配置
    storage_config.validate().await?;
    println!("✅ 存储配置验证成功");

    // === 阶段1: 第一个设备创建数据并上传快照 ===
    println!("\n🔄 阶段1: 创建第一个设备并添加历史数据");
    
    let device1_id = "original_device".to_string();
    let hybrid_storage1 = Arc::new(
        HybridStorageEngine::new(storage_dir.join("device1"), device1_id.clone()).await?
    );
    
    let cloud_sync1 = Arc::new(SyncEngine::new(SyncConfig {
        user_id: "demo_user".to_string(),
        device_id: device1_id,
        storage_operator: storage_config.create_operator().await?,
        sync_interval_seconds: 5,
    }));

    let hybrid_sync1 = Arc::new(HybridSyncEngine::new(
        hybrid_storage1.clone(),
        cloud_sync1
    ));

    // 初始化第一个设备
    hybrid_sync1.initialize().await?;
    println!("✅ 第一个设备初始化完成");

    // 添加大量历史数据
    println!("📋 添加历史数据...");
    for i in 0..15 {
        let item = ClipboardItem {
            id: uuid::Uuid::new_v4().to_string(),
            content: format!("历史数据 #{} - 这是一些重要的历史剪切板内容", i + 1),
            timestamp: chrono::Utc::now().timestamp() as u64,
            item_type: "text".to_string(),
            size: Some(format!("历史数据 #{}", i + 1).len() as u64),
            file_paths: None,
            file_types: None,
        };
        hybrid_sync1.add_item(&item).await?;
        
        // 每隔几个项目暂停一下，模拟真实使用场景
        if i % 3 == 0 {
            sleep(Duration::from_millis(100)).await;
        }
    }
    
    println!("✅ 添加了15个历史数据项目");

    // 等待数据同步到云端
    println!("🔄 同步数据到云端...");
    hybrid_sync1.sync().await?;
    sleep(Duration::from_secs(1)).await;

    // 手动创建快照
    println!("📸 创建数据快照...");
    hybrid_sync1.create_snapshot().await?;
    
    // 显示第一个设备的状态
    let status1 = hybrid_sync1.get_sync_status().await?;
    println!("📊 第一个设备状态: total_items={}, last_snapshot={:?}", 
             status1["hybrid_storage"]["total_items"], 
             status1["last_snapshot"]);

    // === 阶段2: 模拟第一个设备下线，继续添加数据 ===
    println!("\n🔄 阶段2: 第一个设备继续使用，添加更多数据");
    
    for i in 15..20 {
        let item = ClipboardItem {
            id: uuid::Uuid::new_v4().to_string(),
            content: format!("最新数据 #{} - 快照后的新内容", i - 14),
            timestamp: chrono::Utc::now().timestamp() as u64,
            item_type: "text".to_string(),
            size: Some(format!("最新数据 #{}", i - 14).len() as u64),
            file_paths: None,
            file_types: None,
        };
        hybrid_sync1.add_item(&item).await?;
    }
    
    println!("✅ 添加了5个快照后的新数据");
    hybrid_sync1.sync().await?;
    
    let final_status1 = hybrid_sync1.get_sync_status().await?;
    println!("📊 第一个设备最终状态: total_items={}", 
             final_status1["hybrid_storage"]["total_items"]);

    // === 阶段3: 新设备加入并恢复所有历史数据 ===
    println!("\n🔄 阶段3: 新设备加入，测试历史数据恢复");
    
    let device2_id = "new_device".to_string();
    let hybrid_storage2 = Arc::new(
        HybridStorageEngine::new(storage_dir.join("device2"), device2_id.clone()).await?
    );
    
    let cloud_sync2 = Arc::new(SyncEngine::new(SyncConfig {
        user_id: "demo_user".to_string(),
        device_id: device2_id,
        storage_operator: storage_config.create_operator().await?,
        sync_interval_seconds: 5,
    }));

    let hybrid_sync2 = Arc::new(HybridSyncEngine::new(
        hybrid_storage2.clone(),
        cloud_sync2
    ));

    println!("🔄 新设备初始化（应该自动恢复快照数据）...");
    hybrid_sync2.initialize().await?;
    
    // 检查新设备的数据
    let device2_items = hybrid_sync2.get_all_items().await?;
    println!("📊 新设备恢复数据: {} 个项目", device2_items.len());
    
    println!("📋 新设备的前5个历史项目:");
    for (i, item) in device2_items.iter().take(5).enumerate() {
        println!("  {}. {} (时间戳: {})", i + 1, item.content, item.timestamp);
    }
    
    if device2_items.len() > 5 {
        println!("  ... 还有 {} 个项目", device2_items.len() - 5);
    }

    // 验证数据完整性
    let status2 = hybrid_sync2.get_sync_status().await?;
    println!("📊 新设备状态: total_items={}", 
             status2["hybrid_storage"]["total_items"]);

    // === 阶段4: 验证新设备可以正常同步新数据 ===
    println!("\n🔄 阶段4: 验证新设备可以正常同步");
    
    // 新设备添加数据
    let new_item = ClipboardItem {
        id: uuid::Uuid::new_v4().to_string(),
        content: "新设备的第一条数据".to_string(),
        timestamp: chrono::Utc::now().timestamp() as u64,
        item_type: "text".to_string(),
        size: Some(33),
        file_paths: None,
        file_types: None,
    };
    
    hybrid_sync2.add_item(&new_item).await?;
    println!("✅ 新设备添加了1个项目");
    
    // 同步到第一个设备
    sleep(Duration::from_secs(2)).await;
    hybrid_sync1.sync().await?;
    
    let device1_final_items = hybrid_sync1.get_all_items().await?;
    println!("📊 第一个设备同步后: {} 个项目", device1_final_items.len());

    // === 阶段5: 性能和数据完整性验证 ===
    println!("\n📈 数据完整性验证:");
    
    // 搜索测试
    let search_results = hybrid_sync2.search_items("历史数据", 10).await?;
    println!("🔍 新设备搜索'历史数据': {} 个结果", search_results.len());
    
    // 验证特定内容
    let has_first_item = device2_items.iter().any(|item| item.content.contains("历史数据 #1"));
    let has_latest_item = device2_items.iter().any(|item| item.content.contains("最新数据"));
    
    println!("✅ 数据完整性检查:");
    println!("  - 包含第一个历史项目: {}", has_first_item);
    println!("  - 包含快照后数据: {}", has_latest_item);
    println!("  - 总项目数匹配: {}", device1_final_items.len() == device2_items.len() + 1);

    // 显示最终统计
    println!("\n📊 最终统计:");
    println!("  - 第一个设备: {} 项目", device1_final_items.len());
    println!("  - 新设备: {} 项目", device2_items.len());
    println!("  - 快照恢复项目: {} 项目", 
             device2_items.iter().filter(|item| item.content.contains("历史数据")).count());
    println!("  - 增量同步项目: {} 项目", 
             device2_items.iter().filter(|item| item.content.contains("最新数据")).count());

    println!("\n🎉 快照同步机制验证完成！");
    println!("📋 功能确认:");
    println!("  ✅ 新设备自动检测云端快照");
    println!("  ✅ 完整恢复历史数据");
    println!("  ✅ 应用快照后的增量更新");
    println!("  ✅ 正常进行双向同步");
    println!("  ✅ 搜索功能正常");
    println!("  ✅ 数据完整性保证");

    Ok(())
} 