use anyhow::Result;
use clippy_lib::hybrid_storage::HybridStorageEngine;
use clippy_lib::hybrid_sync::HybridSyncEngine;
use clippy_lib::sync::{SyncEngine, SyncConfig};
use clippy_lib::storage::ClipboardItem;
use clippy_lib::storage_adapter::{StorageConfig, StorageBackend};
use std::sync::Arc;
use tempfile::TempDir;
use tokio::time::{sleep, Duration};

#[tokio::main]
async fn main() -> Result<()> {
    // 初始化日志
    tracing_subscriber::fmt::init();

    println!("🚀 混合同步系统演示");
    
    // 创建临时目录用于演示
    let temp_dir = TempDir::new()?;
    let storage_dir = temp_dir.path().to_path_buf();
    let sync_data_dir = temp_dir.path().join("sync_data");
    tokio::fs::create_dir_all(&sync_data_dir).await?;

    println!("📁 使用临时目录: {:?}", storage_dir);

    // 创建存储配置（本地文件系统）
    let storage_config = StorageConfig {
        backend: StorageBackend::FileSystem {
            root_path: sync_data_dir.to_string_lossy().to_string(),
        },
        retry_attempts: 3,
        timeout_seconds: 30,
    };

    // 验证存储配置
    storage_config.validate().await?;
    println!("✅ 存储配置验证成功");

    // 创建两个设备的混合同步引擎
    let device1_id = "demo_device_1".to_string();
    let device2_id = "demo_device_2".to_string();

    // 设备1
    let hybrid_storage1 = Arc::new(
        HybridStorageEngine::new(storage_dir.join("device1"), device1_id.clone()).await?
    );
    
    let cloud_sync1 = Arc::new(SyncEngine::new(SyncConfig {
        user_id: "demo_user".to_string(),
        device_id: device1_id,
        storage_operator: storage_config.create_operator().await?,
        sync_interval_seconds: 5,
    }));

    let hybrid_sync1 = Arc::new(HybridSyncEngine::new(
        hybrid_storage1.clone(),
        cloud_sync1
    ));

    // 设备2
    let hybrid_storage2 = Arc::new(
        HybridStorageEngine::new(storage_dir.join("device2"), device2_id.clone()).await?
    );
    
    let cloud_sync2 = Arc::new(SyncEngine::new(SyncConfig {
        user_id: "demo_user".to_string(),
        device_id: device2_id,
        storage_operator: storage_config.create_operator().await?,
        sync_interval_seconds: 5,
    }));

    let hybrid_sync2 = Arc::new(HybridSyncEngine::new(
        hybrid_storage2.clone(),
        cloud_sync2
    ));

    println!("🔄 创建了两个混合同步引擎（模拟两台设备）");

    // 初始化混合同步引擎
    hybrid_sync1.initialize().await?;
    hybrid_sync2.initialize().await?;
    println!("✅ 混合同步引擎初始化完成");

    // 设备1添加一些数据
    println!("\n📋 设备1 添加剪切板内容...");
    
    let item1 = ClipboardItem {
        id: uuid::Uuid::new_v4().to_string(),
        content: "设备1的第一条数据".to_string(),
        timestamp: chrono::Utc::now().timestamp() as u64,
        item_type: "text".to_string(),
        size: Some(42),
        file_paths: None,
        file_types: None,
    };
    
    let item2 = ClipboardItem {
        id: uuid::Uuid::new_v4().to_string(),
        content: "设备1的第二条数据 - 包含特殊字符 @#$%".to_string(),
        timestamp: chrono::Utc::now().timestamp() as u64,
        item_type: "text".to_string(),
        size: Some(68),
        file_paths: None,
        file_types: None,
    };

    hybrid_sync1.add_item(&item1).await?;
    sleep(Duration::from_millis(100)).await;
    hybrid_sync1.add_item(&item2).await?;
    
    println!("✅ 设备1添加了 2 个项目");

    // 等待同步
    sleep(Duration::from_secs(2)).await;

    // 设备2执行同步
    println!("\n🔄 设备2执行同步...");
    hybrid_sync2.sync().await?;

    // 检查设备2的数据
    let device2_items = hybrid_sync2.get_all_items().await?;
    println!("📊 设备2同步后有 {} 个项目", device2_items.len());

    for (i, item) in device2_items.iter().enumerate() {
        println!("  {}. {} ({})", i + 1, item.content, item.item_type);
    }

    // 设备2添加数据
    println!("\n📋 设备2 添加剪切板内容...");
    
    let item3 = ClipboardItem {
        id: uuid::Uuid::new_v4().to_string(),
        content: "设备2的独有数据".to_string(),
        timestamp: chrono::Utc::now().timestamp() as u64,
        item_type: "text".to_string(),
        size: Some(30),
        file_paths: None,
        file_types: None,
    };

    hybrid_sync2.add_item(&item3).await?;
    println!("✅ 设备2添加了 1 个项目");

    // 等待同步
    sleep(Duration::from_secs(2)).await;

    // 设备1执行同步
    println!("\n🔄 设备1执行同步...");
    hybrid_sync1.sync().await?;

    // 检查设备1的数据
    let device1_items = hybrid_sync1.get_all_items().await?;
    println!("📊 设备1同步后有 {} 个项目", device1_items.len());

    for (i, item) in device1_items.iter().enumerate() {
        println!("  {}. {} ({})", i + 1, item.content, item.item_type);
    }

    // 测试搜索功能
    println!("\n🔍 测试搜索功能...");
    let search_results = hybrid_sync1.search_items("设备1", 10).await?;
    println!("搜索 '设备1' 找到 {} 个结果", search_results.len());

    for item in &search_results {
        println!("  - {}", item.content);
    }

    // 测试删除同步
    println!("\n🗑️ 测试删除同步...");
    if let Some(first_item) = device1_items.first() {
        let deleted = hybrid_sync1.delete_item(&first_item.id).await?;
        println!("删除项目: {} ({})", first_item.content, deleted);
        
        // 等待同步
        sleep(Duration::from_secs(2)).await;
        
        // 设备2同步删除
        hybrid_sync2.sync().await?;
        let device2_items_after_delete = hybrid_sync2.get_all_items().await?;
        println!("设备2同步删除后有 {} 个项目", device2_items_after_delete.len());
    }

    // 显示同步状态
    println!("\n📈 混合同步状态:");
    let status1 = hybrid_sync1.get_sync_status().await?;
    println!("设备1状态: {}", serde_json::to_string_pretty(&status1)?);

    let status2 = hybrid_sync2.get_sync_status().await?;
    println!("设备2状态: {}", serde_json::to_string_pretty(&status2)?);

    // 性能测试
    println!("\n⚡ 性能测试...");
    let start_time = std::time::Instant::now();
    
    for i in 0..100 {
        let item = ClipboardItem {
            id: uuid::Uuid::new_v4().to_string(),
            content: format!("性能测试项目 #{}", i),
            timestamp: chrono::Utc::now().timestamp() as u64,
            item_type: "text".to_string(),
            size: Some(format!("性能测试项目 #{}", i).len() as u64),
            file_paths: None,
            file_types: None,
        };
        hybrid_storage1.insert(&item).await?;
    }
    
    let insert_duration = start_time.elapsed();
    println!("插入100个项目耗时: {:?} ({:.2} ops/s)", 
             insert_duration, 
             100.0 / insert_duration.as_secs_f64());

    // 查询性能测试
    let start_time = std::time::Instant::now();
    let _all_items = hybrid_storage1.get_all().await?;
    let query_duration = start_time.elapsed();
    println!("查询所有项目耗时: {:?}", query_duration);

    // 搜索性能测试
    let start_time = std::time::Instant::now();
    let _search_results = hybrid_storage1.search("性能测试", 50).await?;
    let search_duration = start_time.elapsed();
    println!("搜索耗时: {:?}", search_duration);

    println!("\n🎉 混合同步系统演示完成！");
    println!("📋 功能验证:");
    println!("  ✅ 双向数据同步");
    println!("  ✅ 内容去重机制");
    println!("  ✅ 删除操作同步");
    println!("  ✅ 全文搜索功能");
    println!("  ✅ 高性能存储");
    println!("  ✅ 状态监控");

    Ok(())
} 