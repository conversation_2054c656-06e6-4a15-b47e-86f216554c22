use anyhow::Result;
use clippy_lib::hybrid_storage::HybridStorageEngine;
use clippy_lib::storage::ClipboardItem;
use std::sync::Arc;
use tempfile::TempDir;
use tokio::time::{sleep, Duration};

#[tokio::main]
async fn main() -> Result<()> {
    // 初始化日志
    tracing_subscriber::fmt::init();

    println!("🚀 前端界面集成演示 - 混合存储架构");
    
    // 创建临时目录用于演示
    let temp_dir = TempDir::new()?;
    let storage_dir = temp_dir.path().to_path_buf();

    println!("📁 使用临时目录: {:?}", storage_dir);

    // 创建混合存储引擎
    let device_id = "demo_device".to_string();
    let hybrid_storage = Arc::new(
        HybridStorageEngine::new(storage_dir.clone(), device_id).await?
    );
    
    println!("✅ 混合存储引擎初始化完成");

    // 添加一些测试数据以便前端显示
    println!("📋 添加测试数据...");
    
    let test_items = vec![
        ClipboardItem {
            id: uuid::Uuid::new_v4().to_string(),
            content: "这是第一个测试文本内容".to_string(),
            timestamp: chrono::Utc::now().timestamp() as u64,
            item_type: "text".to_string(),
            size: Some(45),
            file_paths: None,
            file_types: None,
        },
        ClipboardItem {
            id: uuid::Uuid::new_v4().to_string(),
            content: "JavaScript代码示例: function hello() { console.log('Hello World!'); }".to_string(),
            timestamp: chrono::Utc::now().timestamp() as u64 - 300,
            item_type: "text".to_string(),
            size: Some(67),
            file_paths: None,
            file_types: None,
        },
        ClipboardItem {
            id: uuid::Uuid::new_v4().to_string(),
            content: "SQL查询: SELECT * FROM users WHERE active = true ORDER BY created_at DESC".to_string(),
            timestamp: chrono::Utc::now().timestamp() as u64 - 600,
            item_type: "text".to_string(),
            size: Some(73),
            file_paths: None,
            file_types: None,
        },
        ClipboardItem {
            id: uuid::Uuid::new_v4().to_string(),
            content: "/Users/<USER>/Documents/report.pdf".to_string(),
            timestamp: chrono::Utc::now().timestamp() as u64 - 900,
            item_type: "files".to_string(),
            size: Some(1024 * 1024 * 2), // 2MB
            file_paths: Some(vec!["/Users/<USER>/Documents/report.pdf".to_string()]),
            file_types: None,
        },
    ];

    for item in &test_items {
        hybrid_storage.insert(item).await?;
        sleep(Duration::from_millis(100)).await;
    }

    println!("✅ 添加了 {} 个测试数据项目", test_items.len());

    // 验证数据已正确存储
    let all_items = hybrid_storage.get_all().await?;
    println!("📊 存储验证: {} 个项目已保存", all_items.len());

    // 测试搜索功能
    let search_results = hybrid_storage.search("JavaScript", 10).await?;
    println!("🔍 搜索测试: 找到 {} 个包含'JavaScript'的项目", search_results.len());

    // 显示存储统计
    let stats = hybrid_storage.get_stats().await?;
    println!("📈 存储统计:");
    println!("  - 总项目: {}", stats.total_items);
    println!("  - 活跃项目: {}", stats.active_items);
    println!("  - 数据库大小: {} 字节", stats.database_size);
    println!("  - Oplog大小: {} 字节", stats.oplog_size);

    // 测试分页功能
    let paginated = hybrid_storage.get_paginated(2, 0).await?;
    println!("📄 分页测试: 获取前2个项目 - {} 个结果", paginated.len());

    // 演示内容去重
    println!("\n🔄 测试内容去重功能...");
    let duplicate_item = ClipboardItem {
        id: uuid::Uuid::new_v4().to_string(),
        content: "这是第一个测试文本内容".to_string(), // 重复内容
        timestamp: chrono::Utc::now().timestamp() as u64,
        item_type: "text".to_string(),
        size: Some(45),
        file_paths: None,
        file_types: None,
    };

    hybrid_storage.insert(&duplicate_item).await?;
    
    let final_items = hybrid_storage.get_all().await?;
    println!("✅ 去重测试: 尝试插入重复内容后，仍有 {} 个项目（应该还是{}个）", 
             final_items.len(), test_items.len());

    println!("\n🎉 演示完成！现在可以启动前端应用测试混合存储界面:");
    println!("   cargo tauri dev");
    println!("\n📋 前端功能验证清单:");
    println!("   ✅ 切换到'混合存储'标签页");
    println!("   ✅ 查看存储统计信息");
    println!("   ✅ 测试全文搜索功能");
    println!("   ✅ 创建快照（如果同步已配置）");
    println!("   ✅ 执行立即同步");
    println!("   ✅ 查看性能指标");
    println!("   ✅ 执行存储清理");

    Ok(())
} 