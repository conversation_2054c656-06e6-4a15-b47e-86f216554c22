{"$schema": "https://schema.tauri.app/config/2", "productName": "clippy", "version": "0.1.0", "identifier": "com.clippy.app", "build": {"beforeDevCommand": "npm run dev", "devUrl": "http://localhost:1420", "beforeBuildCommand": "npm run build", "frontendDist": "../dist"}, "app": {"windows": [{"label": "main", "title": "Clippy - 剪贴板管理器", "width": 800, "height": 600, "minWidth": 400, "minHeight": 300, "center": true, "resizable": true, "maximizable": true, "minimizable": true, "closable": true, "decorations": true, "alwaysOnTop": false, "skipTaskbar": false, "visible": true}], "security": {"csp": null}, "trayIcon": {"iconPath": "icons/32x32.png", "iconAsTemplate": false, "menuOnLeftClick": false, "title": "<PERSON><PERSON><PERSON>"}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}}