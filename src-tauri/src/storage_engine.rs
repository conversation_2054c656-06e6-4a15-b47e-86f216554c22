use anyhow::Result;
use sqlx::{Row, SqlitePool};
use std::path::Path;

use crate::models::{ClipboardItem, Tombstone, Operation, StorageStats, ApplyResult, OperationType, BlobManifest, BlobChunk};
use crate::hlc::{HLC, HLCGenerator};

/// SQLite 存储引擎
#[derive(Clone)]
pub struct StorageEngine {
    pool: SqlitePool,
    device_id: String,
    pub hlc_generator: HLCGenerator,
}

impl StorageEngine {
    /// 创建新的存储引擎
    pub async fn new<P: AsRef<Path>>(db_path: P, device_id: String) -> Result<Self> {
        let db_url = format!("sqlite://{}", db_path.as_ref().display());
        
        let pool = SqlitePool::connect(&db_url).await?;
        
        let mut engine = Self {
            pool,
            device_id,
            hlc_generator: HLCGenerator::new(),
        };
        
        engine.init_schema().await?;
        Ok(engine)
    }

    /// 初始化数据库模式（LWW-oplog架构）
    async fn init_schema(&self) -> Result<()> {
        // 启用 WAL 模式
        sqlx::query("PRAGMA journal_mode=WAL").execute(&self.pool).await?;
        sqlx::query("PRAGMA synchronous=NORMAL").execute(&self.pool).await?;
        sqlx::query("PRAGMA cache_size=10000").execute(&self.pool).await?;

        // 创建 clipboard_item 表
        sqlx::query(r#"
            CREATE TABLE IF NOT EXISTS clipboard_item (
                item_id TEXT PRIMARY KEY,
                content BLOB,
                mime TEXT NOT NULL,
                created_ts INTEGER NOT NULL,
                blob_id TEXT,
                size INTEGER NOT NULL,
                file_name TEXT,
                device_id TEXT NOT NULL
            )
        "#).execute(&self.pool).await?;

        // 创建 tombstone 表
        sqlx::query(r#"
            CREATE TABLE IF NOT EXISTS tombstone (
                item_id TEXT PRIMARY KEY,
                deleted_ts INTEGER NOT NULL,
                device_id TEXT NOT NULL
            )
        "#).execute(&self.pool).await?;

        // 创建 oplog 表（HLC字符串作为主键保证顺序）
        sqlx::query(r#"
            CREATE TABLE IF NOT EXISTS oplog (
                op_id TEXT PRIMARY KEY,
                item_id TEXT NOT NULL,
                op INTEGER NOT NULL,
                payload BLOB,
                device_id TEXT NOT NULL,
                created_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now') * 1000)
            )
        "#).execute(&self.pool).await?;

        // 创建 blob_manifest 表
        sqlx::query(r#"
            CREATE TABLE IF NOT EXISTS blob_manifest (
                blob_id TEXT PRIMARY KEY,
                sha256 TEXT NOT NULL,
                size INTEGER NOT NULL,
                mime_type TEXT NOT NULL,
                file_name TEXT,
                created_ts INTEGER NOT NULL
            )
        "#).execute(&self.pool).await?;

        // 创建 blob_chunk 表
        sqlx::query(r#"
            CREATE TABLE IF NOT EXISTS blob_chunk (
                blob_id TEXT NOT NULL,
                chunk_index INTEGER NOT NULL,
                etag TEXT NOT NULL,
                size INTEGER NOT NULL,
                PRIMARY KEY (blob_id, chunk_index)
            )
        "#).execute(&self.pool).await?;

        // 创建索引
        sqlx::query("CREATE INDEX IF NOT EXISTS idx_clipboard_created_ts ON clipboard_item(created_ts DESC)").execute(&self.pool).await?;
        sqlx::query("CREATE INDEX IF NOT EXISTS idx_tombstone_deleted_ts ON tombstone(deleted_ts DESC)").execute(&self.pool).await?;
        sqlx::query("CREATE INDEX IF NOT EXISTS idx_oplog_device_ts ON oplog(device_id, op_id)").execute(&self.pool).await?;
        sqlx::query("CREATE INDEX IF NOT EXISTS idx_blob_manifest_created ON blob_manifest(created_ts DESC)").execute(&self.pool).await?;

        Ok(())
    }

    /// 添加剪贴板项目（LWW-oplog架构）
    pub async fn add_item(&self, mut item: ClipboardItem) -> Result<Operation> {
        let hlc = self.hlc_generator.next();
        item.created_ts = hlc;
        item.device_id = self.device_id.clone();

        // 应用 LWW 决议
        let apply_result = self.apply_add_operation(&item).await?;

        // 创建操作记录
        let op = Operation::new_add(item.clone(), hlc, self.device_id.clone());

        if apply_result.applied {
            // 插入到数据库
            self.insert_item(&item).await?;
        }

        // 记录到 oplog（无论是否应用都要记录）
        self.append_to_oplog(&op).await?;

        Ok(op)
    }

    /// 删除剪贴板项目
    pub async fn delete_item(&self, item_id: &str) -> Result<Option<Operation>> {
        let hlc = self.hlc_generator.next();
        
        // 检查项目是否存在
        let exists = self.item_exists(item_id).await?;
        if !exists {
            return Ok(None);
        }
        
        // 应用 LWW 决议
        let apply_result = self.apply_delete_operation(item_id, hlc).await?;
        
        // 创建操作记录
        let op = Operation::new_delete(item_id.to_string(), hlc, self.device_id.clone());
        
        if apply_result.applied {
            // 删除项目并添加墓碑
            self.delete_item_internal(item_id, hlc).await?;
        }
        
        // 记录到 oplog
        self.append_to_oplog(&op).await?;
        
        Ok(Some(op))
    }

    /// 应用远程操作（同步时使用）
    pub async fn apply_operation(&self, op: &Operation) -> Result<ApplyResult> {
        // 更新本地 HLC
        let op_hlc = op.hlc().map_err(|e| anyhow::anyhow!("无效的 HLC: {}", e))?;
        self.hlc_generator.update(op_hlc);

        match op.op {
            OperationType::Add => {
                if let Some(item) = op.get_item().map_err(|e| anyhow::anyhow!(e))? {
                    let apply_result = self.apply_add_operation(&item).await?;
                    if apply_result.applied {
                        self.insert_item(&item).await?;
                    }
                    Ok(apply_result)
                } else {
                    Ok(ApplyResult::reject("ADD 操作缺少 payload"))
                }
            }
            OperationType::Delete => {
                let apply_result = self.apply_delete_operation(&op.item_id, op_hlc).await?;
                if apply_result.applied {
                    self.delete_item_internal(&op.item_id, op_hlc).await?;
                }
                Ok(apply_result)
            }
        }
    }

    /// 应用 ADD 操作（LWW 决议）
    async fn apply_add_operation(&self, item: &ClipboardItem) -> Result<ApplyResult> {
        // 检查是否已被删除且删除时间更新
        if let Some(tombstone) = self.get_tombstone(&item.item_id).await? {
            if tombstone.deleted_ts.happens_after(&item.created_ts) {
                return Ok(ApplyResult::reject("项目已被更新的删除操作删除"));
            }
            // 删除操作更旧，移除墓碑
            self.remove_tombstone(&item.item_id).await?;
        }

        // 检查是否已有更新的 ADD 操作
        if let Some(existing_item) = self.get_item(&item.item_id).await? {
            if existing_item.created_ts.happens_after(&item.created_ts) {
                return Ok(ApplyResult::reject("已有更新的 ADD 操作"));
            }
        }

        Ok(ApplyResult::accept("ADD 操作被接受"))
    }

    /// 应用 DELETE 操作（LWW 决议）
    async fn apply_delete_operation(&self, item_id: &str, deleted_ts: HLC) -> Result<ApplyResult> {
        // 检查项目是否存在且创建时间更旧
        if let Some(item) = self.get_item(item_id).await? {
            if item.created_ts.happens_after(&deleted_ts) {
                return Ok(ApplyResult::reject("项目的创建时间比删除时间更新"));
            }
        }

        // 检查是否已有更新的删除操作
        if let Some(tombstone) = self.get_tombstone(item_id).await? {
            if tombstone.deleted_ts.happens_after(&deleted_ts) {
                return Ok(ApplyResult::reject("已有更新的删除操作"));
            }
        }

        Ok(ApplyResult::accept("DELETE 操作被接受"))
    }

    /// 内部方法：插入项目到数据库
    async fn insert_item(&self, item: &ClipboardItem) -> Result<()> {
        sqlx::query(r#"
            INSERT OR REPLACE INTO clipboard_item
            (item_id, content, mime, created_ts, blob_id, size, file_name, device_id)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        "#)
        .bind(&item.item_id)
        .bind(&item.content)
        .bind(&item.mime)
        .bind(item.created_ts.0 as i64)
        .bind(&item.blob_id)
        .bind(item.size as i64)
        .bind(&item.file_name)
        .bind(&item.device_id)
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    /// 内部方法：删除项目并添加墓碑
    async fn delete_item_internal(&self, item_id: &str, deleted_ts: HLC) -> Result<()> {
        let mut tx = self.pool.begin().await?;

        // 删除项目
        sqlx::query("DELETE FROM clipboard_item WHERE item_id = ?")
            .bind(item_id)
            .execute(&mut *tx)
            .await?;

        // 添加墓碑
        sqlx::query(r#"
            INSERT OR REPLACE INTO tombstone (item_id, deleted_ts, device_id)
            VALUES (?, ?, ?)
        "#)
        .bind(item_id)
        .bind(deleted_ts.0 as i64)
        .bind(&self.device_id)
        .execute(&mut *tx)
        .await?;

        tx.commit().await?;
        Ok(())
    }

    /// 获取项目
    pub async fn get_item(&self, item_id: &str) -> Result<Option<ClipboardItem>> {
        let row = sqlx::query(r#"
            SELECT item_id, content, mime, created_ts, blob_id, size, file_name, device_id
            FROM clipboard_item WHERE item_id = ?
        "#)
        .bind(item_id)
        .fetch_optional(&self.pool)
        .await?;

        if let Some(row) = row {
            // 需要推断 content_type
            let mime: String = row.get("mime");
            let content_type = if mime.starts_with("text/") {
                crate::models::ContentType::Text
            } else if mime.starts_with("image/") {
                crate::models::ContentType::Image
            } else {
                crate::models::ContentType::File
            };

            Ok(Some(ClipboardItem {
                item_id: row.get("item_id"),
                content_type,
                content: row.get("content"),
                mime,
                created_ts: HLC(row.get::<i64, _>("created_ts") as u64),
                device_id: row.get("device_id"),
                size: row.get::<i64, _>("size") as u64,
                blob_id: row.get("blob_id"),
                file_name: row.get("file_name"),
            }))
        } else {
            Ok(None)
        }
    }

    /// 获取墓碑
    async fn get_tombstone(&self, item_id: &str) -> Result<Option<Tombstone>> {
        let row = sqlx::query(r#"
            SELECT item_id, deleted_ts, device_id
            FROM tombstone WHERE item_id = ?
        "#)
        .bind(item_id)
        .fetch_optional(&self.pool)
        .await?;

        if let Some(row) = row {
            Ok(Some(Tombstone {
                item_id: row.get("item_id"),
                deleted_ts: HLC(row.get::<i64, _>("deleted_ts") as u64),
                device_id: row.get("device_id"),
            }))
        } else {
            Ok(None)
        }
    }

    /// 移除墓碑
    async fn remove_tombstone(&self, item_id: &str) -> Result<()> {
        sqlx::query("DELETE FROM tombstone WHERE item_id = ?")
            .bind(item_id)
            .execute(&self.pool)
            .await?;
        Ok(())
    }

    /// 检查项目是否存在
    async fn item_exists(&self, item_id: &str) -> Result<bool> {
        let count: i64 = sqlx::query_scalar("SELECT COUNT(*) FROM clipboard_item WHERE item_id = ?")
            .bind(item_id)
            .fetch_one(&self.pool)
            .await?;
        Ok(count > 0)
    }

    /// 记录操作到 oplog
    async fn append_to_oplog(&self, op: &Operation) -> Result<()> {
        let payload = if let Some(ref item) = op.payload {
            Some(serde_json::to_vec(item)?)
        } else {
            None
        };

        sqlx::query(r#"
            INSERT INTO oplog (op_id, item_id, op, payload, device_id)
            VALUES (?, ?, ?, ?, ?)
        "#)
        .bind(&op.op_id)
        .bind(&op.item_id)
        .bind(op.op as u8)
        .bind(&payload)
        .bind(&op.device_id)
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    /// 获取所有有效项目
    pub async fn get_all_items(&self) -> Result<Vec<ClipboardItem>> {
        let rows = sqlx::query(r#"
            SELECT item_id, content, mime, created_ts, blob_id, size, file_name, device_id
            FROM clipboard_item
            ORDER BY created_ts DESC
        "#)
        .fetch_all(&self.pool)
        .await?;

        let mut items = Vec::new();
        for row in rows {
            // 推断 content_type
            let mime: String = row.get("mime");
            let content_type = if mime.starts_with("text/") {
                crate::models::ContentType::Text
            } else if mime.starts_with("image/") {
                crate::models::ContentType::Image
            } else {
                crate::models::ContentType::File
            };

            items.push(ClipboardItem {
                item_id: row.get("item_id"),
                content_type,
                content: row.get("content"),
                mime,
                created_ts: HLC(row.get::<i64, _>("created_ts") as u64),
                device_id: row.get("device_id"),
                size: row.get::<i64, _>("size") as u64,
                blob_id: row.get("blob_id"),
                file_name: row.get("file_name"),
            });
        }

        Ok(items)
    }

    /// 获取待同步的操作（从指定 HLC 之后）
    pub async fn get_operations_since(&self, device_id: &str, since_hlc: Option<HLC>) -> Result<Vec<Operation>> {
        let since_value = since_hlc.map(|hlc| hlc.to_string()).unwrap_or_else(|| "0".to_string());

        let rows = sqlx::query(r#"
            SELECT op_id, item_id, op, payload, device_id, created_at
            FROM oplog
            WHERE device_id = ? AND op_id > ?
            ORDER BY op_id ASC
        "#)
        .bind(device_id)
        .bind(&since_value)
        .fetch_all(&self.pool)
        .await?;

        let mut operations = Vec::new();
        for row in rows {
            let op_type = match row.get::<i64, _>("op") {
                0 => OperationType::Add,
                1 => OperationType::Delete,
                _ => continue,
            };

            operations.push(Operation {
                op_id: row.get("op_id"),
                item_id: row.get("item_id"),
                op: op_type,
                payload: row.get("payload"),
                device_id: row.get("device_id"),
                created_at: row.get("created_at"),
            });
        }

        Ok(operations)
    }

    /// 清空所有数据
    pub async fn clear_all(&self) -> Result<()> {
        let mut tx = self.pool.begin().await?;

        sqlx::query("DELETE FROM clipboard_item").execute(&mut *tx).await?;
        sqlx::query("DELETE FROM tombstone").execute(&mut *tx).await?;
        sqlx::query("DELETE FROM oplog").execute(&mut *tx).await?;

        tx.commit().await?;
        Ok(())
    }

    /// 获取存储统计信息
    pub async fn get_stats(&self) -> Result<StorageStats> {
        let total_items: i64 = sqlx::query_scalar("SELECT COUNT(*) FROM clipboard_item")
            .fetch_one(&self.pool).await?;
        
        let deleted_items: i64 = sqlx::query_scalar("SELECT COUNT(*) FROM tombstone")
            .fetch_one(&self.pool).await?;
        
        let pending_ops: i64 = sqlx::query_scalar("SELECT COUNT(*) FROM oplog")
            .fetch_one(&self.pool).await?;

        // 计算数据库大小
        let db_size: i64 = sqlx::query_scalar("SELECT page_count * page_size FROM pragma_page_count(), pragma_page_size()")
            .fetch_one(&self.pool).await.unwrap_or(0);

        Ok(StorageStats {
            total_items: total_items as usize,
            active_items: total_items as usize,
            deleted_items: deleted_items as usize,
            pending_ops: pending_ops as usize,
            database_size: db_size as u64,
            oplog_size: 0, // TODO: 计算 oplog 大小
            blob_cache_size: 0, // TODO: 计算 blob 缓存大小
        })
    }

    /// VACUUM 数据库
    pub async fn vacuum(&self) -> Result<()> {
        sqlx::query("VACUUM").execute(&self.pool).await?;
        Ok(())
    }

    /// 更新 HLC 生成器（接收远程 HLC 时）
    pub fn update_hlc(&self, remote_hlc: HLC) {
        self.hlc_generator.update(remote_hlc)
    }

    /// 保存 BLOB manifest
    pub async fn save_blob_manifest(&self, manifest: &BlobManifest) -> Result<()> {
        sqlx::query(r#"
            INSERT OR REPLACE INTO blob_manifest
            (blob_id, sha256, size, mime_type, file_name, created_ts)
            VALUES (?, ?, ?, ?, ?, ?)
        "#)
        .bind(&manifest.blob_id)
        .bind(&manifest.sha256)
        .bind(manifest.size as i64)
        .bind(&manifest.mime_type)
        .bind(&manifest.file_name)
        .bind(manifest.created_ts)
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    /// 获取 BLOB manifest
    pub async fn get_blob_manifest(&self, blob_id: &str) -> Result<Option<BlobManifest>> {
        let row = sqlx::query(r#"
            SELECT blob_id, sha256, size, mime_type, file_name, created_ts
            FROM blob_manifest WHERE blob_id = ?
        "#)
        .bind(blob_id)
        .fetch_optional(&self.pool)
        .await?;

        if let Some(row) = row {
            Ok(Some(BlobManifest {
                blob_id: row.get("blob_id"),
                sha256: row.get("sha256"),
                size: row.get::<i64, _>("size") as u64,
                mime_type: row.get("mime_type"),
                file_name: row.get("file_name"),
                created_ts: row.get("created_ts"),
            }))
        } else {
            Ok(None)
        }
    }

    /// 保存 BLOB 分块信息
    pub async fn save_blob_chunks(&self, chunks: &[BlobChunk]) -> Result<()> {
        let mut tx = self.pool.begin().await?;

        for chunk in chunks {
            sqlx::query(r#"
                INSERT OR REPLACE INTO blob_chunk
                (blob_id, chunk_index, etag, size)
                VALUES (?, ?, ?, ?)
            "#)
            .bind(&chunk.blob_id)
            .bind(chunk.chunk_index as i64)
            .bind(&chunk.etag)
            .bind(chunk.size as i64)
            .execute(&mut *tx)
            .await?;
        }

        tx.commit().await?;
        Ok(())
    }

    /// 获取 BLOB 分块信息
    pub async fn get_blob_chunks(&self, blob_id: &str) -> Result<Vec<BlobChunk>> {
        let rows = sqlx::query(r#"
            SELECT blob_id, chunk_index, etag, size
            FROM blob_chunk WHERE blob_id = ?
            ORDER BY chunk_index ASC
        "#)
        .bind(blob_id)
        .fetch_all(&self.pool)
        .await?;

        let mut chunks = Vec::new();
        for row in rows {
            chunks.push(BlobChunk {
                blob_id: row.get("blob_id"),
                chunk_index: row.get::<i64, _>("chunk_index") as u32,
                etag: row.get("etag"),
                size: row.get::<i64, _>("size") as u64,
            });
        }

        Ok(chunks)
    }

    /// 获取所有墓碑
    pub async fn get_all_tombstones(&self) -> Result<Vec<Tombstone>> {
        let rows = sqlx::query(r#"
            SELECT item_id, deleted_ts, device_id
            FROM tombstone
            ORDER BY deleted_ts DESC
        "#)
        .fetch_all(&self.pool)
        .await?;

        let mut tombstones = Vec::new();
        for row in rows {
            tombstones.push(Tombstone {
                item_id: row.get("item_id"),
                deleted_ts: HLC(row.get::<i64, _>("deleted_ts") as u64),
                device_id: row.get("device_id"),
            });
        }

        Ok(tombstones)
    }
}