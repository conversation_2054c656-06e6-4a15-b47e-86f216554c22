use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use crate::hlc::HLC;
use sha2::{Digest, Sha256};
use uuid::Uuid;

/// Content type enumeration for clipboard items
#[derive(Debug, <PERSON><PERSON>, <PERSON>ialEq, Eq, Serialize, Deserialize)]
pub enum ContentType {
    Text,
    Image,
    File,
}

impl ContentType {
    pub fn as_str(&self) -> &'static str {
        match self {
            ContentType::Text => "text",
            ContentType::Image => "image",
            ContentType::File => "file",
        }
    }

    pub fn from_str(s: &str) -> Option<Self> {
        match s {
            "text" => Some(ContentType::Text),
            "image" => Some(ContentType::Image),
            "file" => Some(ContentType::File),
            _ => None,
        }
    }
}

/// Main clipboard item structure for the LWW-oplog architecture
///
/// This represents a single clipboard entry in the system.
/// Based on the new architecture with content-addressed storage.
#[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON>ize, Deserialize)]
pub struct ClipboardItem {
    /// Unique identifier for this item
    pub item_id: String,
    /// Content type (text, image, file)
    pub content_type: ContentType,
    /// For small content (<256KB): actual content data (text, base64, etc.)
    /// For large content: empty string (content stored in blob)
    pub content: Option<Vec<u8>>,
    /// MIME type of the content
    pub mime: String,
    /// HLC timestamp when this item was created
    pub created_ts: HLC,
    /// Device that created this item
    pub device_id: String,
    /// Size of the content in bytes
    pub size: u64,
    /// For large content: blob_id for content-addressed storage
    pub blob_id: Option<String>,
    /// Original filename for file content
    pub file_name: Option<String>,
}

impl ClipboardItem {
    /// Create a new clipboard item
    pub fn new(
        item_id: String,
        content_type: ContentType,
        content: Option<Vec<u8>>,
        mime: String,
        created_ts: HLC,
        device_id: String,
        size: u64,
        blob_id: Option<String>,
        file_name: Option<String>,
    ) -> Self {
        Self {
            item_id,
            content_type,
            content,
            mime,
            created_ts,
            device_id,
            size,
            blob_id,
            file_name,
        }
    }

    /// Create a text clipboard item
    pub fn new_text(text: String, device_id: String, hlc: HLC) -> Self {
        let content_bytes = text.as_bytes().to_vec();
        let size = content_bytes.len() as u64;

        Self::new(
            uuid::Uuid::new_v4().to_string(),
            ContentType::Text,
            Some(content_bytes),
            "text/plain".to_string(),
            hlc,
            device_id,
            size,
            None,
            None,
        )
    }

    /// Create an image clipboard item
    pub fn new_image(image_data: Vec<u8>, format: String, device_id: String, hlc: HLC) -> Self {
        let size = image_data.len() as u64;
        let mime = format!("image/{}", format);
        let blob_id = if size > 256 * 1024 {
            Some(Self::generate_blob_id(&image_data))
        } else {
            None
        };

        Self::new(
            uuid::Uuid::new_v4().to_string(),
            ContentType::Image,
            if blob_id.is_some() { None } else { Some(image_data) },
            mime,
            hlc,
            device_id,
            size,
            blob_id,
            None,
        )
    }

    /// Create a file clipboard item
    pub fn new_file(file_path: String, file_data: Vec<u8>, device_id: String, hlc: HLC) -> Self {
        let size = file_data.len() as u64;
        let file_name = std::path::Path::new(&file_path)
            .file_name()
            .and_then(|n| n.to_str())
            .unwrap_or("unknown")
            .to_string();

        let mime = infer::get(&file_data)
            .map(|t| t.mime_type().to_string())
            .unwrap_or_else(|| "application/octet-stream".to_string());

        let blob_id = if size > 256 * 1024 {
            Some(Self::generate_blob_id(&file_data))
        } else {
            None
        };

        Self::new(
            uuid::Uuid::new_v4().to_string(),
            ContentType::File,
            if blob_id.is_some() { None } else { Some(file_data) },
            mime,
            hlc,
            device_id,
            size,
            blob_id,
            Some(file_name),
        )
    }
}

/// Operation types for the operation log (LWW-oplog architecture)
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum OperationType {
    Add = 0,
    Delete = 1,
}

impl OperationType {
    pub fn as_str(&self) -> &'static str {
        match self {
            OperationType::Add => "add",
            OperationType::Delete => "delete",
        }
    }

    pub fn from_str(s: &str) -> Option<Self> {
        match s {
            "add" => Some(OperationType::Add),
            "delete" => Some(OperationType::Delete),
            _ => None,
        }
    }

    pub fn from_u8(value: u8) -> Option<Self> {
        match value {
            0 => Some(OperationType::Add),
            1 => Some(OperationType::Delete),
            _ => None,
        }
    }
}

/// Operation log entry for change tracking and sync (LWW-oplog architecture)
///
/// Each operation represents a single change to the clipboard.
/// Operations are immutable and only contain ADD or DELETE.
/// The op_id is the HLC string for ordering.
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Operation {
    /// HLC timestamp as string (used as op_id for ordering)
    pub op_id: String,
    /// ID of the item this operation affects
    pub item_id: String,
    /// Type of operation (0=ADD, 1=DELETE)
    pub op: OperationType,
    /// For ADD operations: serialized ClipboardItem; for DELETE: None
    pub payload: Option<Vec<u8>>,
    /// Device that performed this operation
    pub device_id: String,
    /// Creation timestamp (milliseconds since Unix epoch)
    pub created_at: i64,
}

impl Operation {
    /// Create a new ADD operation
    pub fn new_add(item: ClipboardItem, hlc: HLC, device_id: String) -> Self {
        let payload = serde_json::to_vec(&item).ok();
        Self {
            op_id: hlc.to_string(),
            item_id: item.item_id.clone(),
            op: OperationType::Add,
            payload,
            device_id,
            created_at: chrono::Utc::now().timestamp_millis(),
        }
    }

    /// Create a new DELETE operation
    pub fn new_delete(item_id: String, hlc: HLC, device_id: String) -> Self {
        Self {
            op_id: hlc.to_string(),
            item_id,
            op: OperationType::Delete,
            payload: None,
            device_id,
            created_at: chrono::Utc::now().timestamp_millis(),
        }
    }

    /// Get the HLC from op_id
    pub fn hlc(&self) -> Result<HLC, String> {
        HLC::from_string(&self.op_id)
    }

    /// Get the ClipboardItem from payload (for ADD operations)
    pub fn get_item(&self) -> Result<Option<ClipboardItem>, String> {
        match &self.payload {
            Some(data) => {
                let item: ClipboardItem = serde_json::from_slice(data)
                    .map_err(|e| format!("Failed to deserialize item: {}", e))?;
                Ok(Some(item))
            }
            None => Ok(None),
        }
    }
}

/// Tombstone record for tracking deletions (LWW-oplog architecture)
///
/// Tombstones are permanent records that prevent deleted items
/// from being resurrected during sync operations.
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Tombstone {
    /// ID of the deleted item
    pub item_id: String,
    /// HLC timestamp when the item was deleted (as integer for SQLite)
    pub deleted_ts: HLC,
    /// Device that deleted the item
    pub device_id: String,
}

impl Tombstone {
    pub fn new(item_id: String, deleted_ts: HLC, device_id: String) -> Self {
        Self {
            item_id,
            deleted_ts,
            device_id,
        }
    }
}

/// BLOB manifest for large content management (LWW-oplog architecture)
///
/// Large content (>256KB) is stored separately with content-addressed
/// identifiers for deduplication and efficient storage.
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BlobManifest {
    /// Content-addressed identifier (SHA256 hash)
    pub blob_id: String,
    /// SHA256 hash of the content for integrity
    pub sha256: String,
    /// Total size of the content
    pub size: u64,
    /// Content type/MIME type
    pub mime_type: String,
    /// Original filename (if applicable)
    pub file_name: Option<String>,
    /// Creation timestamp (milliseconds since Unix epoch)
    pub created_ts: i64,
}

/// Information about a single chunk of a large blob
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BlobChunk {
    /// Blob ID this chunk belongs to
    pub blob_id: String,
    /// Chunk index (0-based)
    pub chunk_index: u32,
    /// ETag from cloud storage
    pub etag: String,
    /// Size of this chunk
    pub size: u64,
}

impl BlobManifest {
    pub fn new(blob_id: String, sha256: String, size: u64, mime_type: String, file_name: Option<String>) -> Self {
        Self {
            blob_id,
            sha256,
            size,
            mime_type,
            file_name,
            created_ts: chrono::Utc::now().timestamp_millis(),
        }
    }

    /// Check if this blob needs chunking (>8MB)
    pub fn needs_chunking(&self) -> bool {
        self.size > 8 * 1024 * 1024
    }

    /// Check if this blob should be inlined (<256KB)
    pub fn is_inline(&self) -> bool {
        self.size <= 256 * 1024
    }
}

/// Database snapshot for sync
/// 
/// Snapshots provide a consistent view of the database state
/// and are used for efficient sync operations.
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Snapshot {
    /// HLC timestamp of this snapshot
    pub timestamp: HLC,
    /// Device that created this snapshot
    pub device_id: String,
    /// Number of items in this snapshot
    pub item_count: i64,
    /// Number of operations included
    pub operation_count: i64,
    /// Compressed size of the snapshot
    pub compressed_size: i64,
    /// Uncompressed size of the snapshot
    pub uncompressed_size: i64,
    /// SHA256 hash of the snapshot data
    pub hash: String,
    /// Creation timestamp (milliseconds since Unix epoch)
    pub created_at: i64,
}

impl Snapshot {
    pub fn new(
        timestamp: HLC,
        device_id: String,
        item_count: i64,
        operation_count: i64,
        compressed_size: i64,
        uncompressed_size: i64,
        hash: String,
    ) -> Self {
        Self {
            timestamp,
            device_id,
            item_count,
            operation_count,
            compressed_size,
            uncompressed_size,
            hash,
            created_at: chrono::Utc::now().timestamp_millis(),
        }
    }
}

/// Storage statistics for monitoring (LWW-oplog architecture)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StorageStats {
    /// Total number of clipboard items
    pub total_items: usize,
    /// Number of active (non-deleted) items
    pub active_items: usize,
    /// Number of deleted items (tombstones)
    pub deleted_items: usize,
    /// Total number of operations in the log
    pub pending_ops: usize,
    /// Size of the database file
    pub database_size: u64,
    /// Size of oplog
    pub oplog_size: u64,
    /// Size of BLOB cache
    pub blob_cache_size: u64,
}

impl Default for StorageStats {
    fn default() -> Self {
        Self {
            total_items: 0,
            active_items: 0,
            deleted_items: 0,
            pending_ops: 0,
            database_size: 0,
            oplog_size: 0,
            blob_cache_size: 0,
        }
    }
}

/// LWW 冲突决议结果
#[derive(Debug, Clone, PartialEq)]
pub enum ConflictResolution {
    /// 接受操作
    Accept,
    /// 拒绝操作（已有更新的操作）
    Reject,
    /// 需要进一步处理
    Defer,
}

/// 应用操作的结果
#[derive(Debug, Clone)]
pub struct ApplyResult {
    pub applied: bool,
    pub resolution: ConflictResolution,
    pub message: String,
}

impl ApplyResult {
    pub fn accept(message: &str) -> Self {
        Self {
            applied: true,
            resolution: ConflictResolution::Accept,
            message: message.to_string(),
        }
    }

    pub fn reject(message: &str) -> Self {
        Self {
            applied: false,
            resolution: ConflictResolution::Reject,
            message: message.to_string(),
        }
    }
}

impl ClipboardItem {
    /// 是否为小文件（内联存储）
    pub fn is_inline(&self) -> bool {
        self.size <= 256 * 1024 // 256 KiB
    }

    /// 是否为大文件（需要分块）
    pub fn needs_chunking(&self) -> bool {
        self.size > 8 * 1024 * 1024 // 8 MiB
    }

    /// 生成 blob_id（基于内容 SHA256）
    pub fn generate_blob_id(content: &[u8]) -> String {
        let mut hasher = Sha256::new();
        hasher.update(content);
        let hash = hasher.finalize();
        format!("{:x}", hash)[..32].to_string() // 取前 32 字符
    }

    /// 获取内容的文本表示（用于显示）
    pub fn get_display_content(&self) -> String {
        match self.content_type {
            ContentType::Text => {
                if let Some(ref content) = self.content {
                    String::from_utf8_lossy(content).to_string()
                } else {
                    format!("Large text content ({})", self.format_size())
                }
            }
            ContentType::Image => {
                format!("Image: {} ({})",
                    self.file_name.as_deref().unwrap_or("image"),
                    self.format_size())
            }
            ContentType::File => {
                format!("File: {} ({})",
                    self.file_name.as_deref().unwrap_or("unknown"),
                    self.format_size())
            }
        }
    }

    /// 格式化文件大小
    pub fn format_size(&self) -> String {
        let size = self.size as f64;
        if size < 1024.0 {
            format!("{} B", size)
        } else if size < 1024.0 * 1024.0 {
            format!("{:.1} KB", size / 1024.0)
        } else if size < 1024.0 * 1024.0 * 1024.0 {
            format!("{:.1} MB", size / (1024.0 * 1024.0))
        } else {
            format!("{:.1} GB", size / (1024.0 * 1024.0 * 1024.0))
        }
    }
}