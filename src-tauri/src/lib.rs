// Prevents additional console window on Windows in release, DO NOT REMOVE!!
#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

use clipboard_rs::{
    Clipboard, ClipboardContext, ClipboardHandler, ClipboardWatcher, 
    ClipboardWatcherContext, ContentFormat
};
use std::sync::{Arc, Mutex};
use std::thread;
use std::path::{Path, PathBuf};
use tauri::{App<PERSON>and<PERSON>, Emitter, Manager};
use infer;
use tauri::tray::{TrayIconBuilder, TrayIconEvent};
use base64::{Engine as _, engine::general_purpose};
use std::fs;
use std::collections::HashMap;
use tauri_plugin_shell::ShellExt;
use arboard::{Clipboard as ArboardClipboard, ImageData};
use clipboard_master::{Master};

// 导入新的模块（暂时禁用）
// pub mod hlc;
// pub mod models;
// pub mod storage_engine;
// pub mod cloud_sync;
// pub mod blob_manager;
// pub mod new_sync_engine;

// 保留原有模块作为兼容
pub mod storage;
pub mod storage_adapter;
pub mod sync;
pub mod hybrid_storage;
pub mod hybrid_sync;

// 使用新的模型和引擎
use hlc::{HLC, HLCGenerator};
use models::{ClipboardItem as NewClipboardItem, StorageStats as NewStorageStats};
use storage_engine::StorageEngine;
use cloud_sync::CloudSyncEngine;
use blob_manager::BlobManager;

// 兼容性类型
use storage::{ClipboardItem, StorageStats, FileTypeInfo};
use hybrid_storage::{HybridStorageEngine, HybridStorageStats};
use sync::{SyncEngine, SyncConfig};
use storage_adapter::StorageConfig;
use hybrid_sync::HybridSyncEngine;

use new_sync_engine::NewSyncEngine;

// 全局状态
type ClipboardStorage = Arc<Mutex<storage::StorageEngine>>;
type HybridClipboardStorage = Arc<HybridStorageEngine>;
type ClipboardSync = Arc<SyncEngine>;
type ClipboardSyncContainer = Arc<Mutex<Option<ClipboardSync>>>;
type HybridSyncContainer = Arc<Mutex<Option<Arc<HybridSyncEngine>>>>;

// 新的同步引擎容器
type NewSyncEngineContainer = Arc<Mutex<Option<Arc<NewSyncEngine>>>>;
type NewStorageEngineContainer = Arc<Mutex<Option<Arc<StorageEngine>>>>;
type BlobManagerContainer = Arc<Mutex<Option<Arc<BlobManager>>>>;

#[tauri::command]
fn get_clipboard_history(state: tauri::State<ClipboardStorage>) -> Vec<ClipboardItem> {
    state.lock().unwrap().get_all()
}

#[tauri::command]
fn clear_clipboard_history(state: tauri::State<ClipboardStorage>) -> Result<(), String> {
    state.lock().unwrap().clear_all().map_err(|e| e.to_string())
}

#[tauri::command]
fn delete_clipboard_item(item_id: String, state: tauri::State<ClipboardStorage>) -> Result<(), String> {
    state.lock().unwrap().delete(&item_id).map_err(|e| e.to_string())
}

#[tauri::command]
fn get_storage_stats(state: tauri::State<ClipboardStorage>) -> StorageStats {
    state.lock().unwrap().stats()
}

#[tauri::command]
fn compact_storage(state: tauri::State<ClipboardStorage>) -> Result<(), String> {
    state.lock().unwrap().compact().map_err(|e| e.to_string())
}

// 混合存储相关命令
#[tauri::command]
async fn hybrid_get_clipboard_history(state: tauri::State<'_, HybridClipboardStorage>) -> Result<Vec<ClipboardItem>, String> {
    state.get_all().await.map_err(|e| e.to_string())
}

#[tauri::command]
async fn hybrid_get_clipboard_history_paginated(
    limit: i64, 
    offset: i64,
    state: tauri::State<'_, HybridClipboardStorage>
) -> Result<Vec<ClipboardItem>, String> {
    state.get_paginated(limit, offset).await.map_err(|e| e.to_string())
}

#[tauri::command]
async fn hybrid_search_clipboard(
    query: String,
    limit: i64,
    state: tauri::State<'_, HybridClipboardStorage>
) -> Result<Vec<ClipboardItem>, String> {
    state.search(&query, limit).await.map_err(|e| e.to_string())
}

#[tauri::command]
async fn hybrid_get_storage_stats(state: tauri::State<'_, HybridClipboardStorage>) -> Result<HybridStorageStats, String> {
    state.get_stats().await.map_err(|e| e.to_string())
}

#[tauri::command]
async fn hybrid_clear_clipboard_history(state: tauri::State<'_, HybridClipboardStorage>) -> Result<(), String> {
    state.clear_all().await.map_err(|e| e.to_string())
}

#[tauri::command]
async fn hybrid_vacuum_storage(state: tauri::State<'_, HybridClipboardStorage>) -> Result<(), String> {
    state.vacuum().await.map_err(|e| e.to_string())
}

#[tauri::command]
fn copy_to_clipboard(content: String) -> Result<(), String> {
    let ctx = ClipboardContext::new().map_err(|e| e.to_string())?;
    ctx.set_text(content).map_err(|e| e.to_string())?;
    Ok(())
}

#[tauri::command]
fn copy_image_to_clipboard(base64_data: String) -> Result<(), String> {
    let ctx = ClipboardContext::new().map_err(|e| e.to_string())?;
    
    // 解码 base64 数据
    let image_bytes = general_purpose::STANDARD
        .decode(base64_data)
        .map_err(|e| format!("Failed to decode base64: {}", e))?;
    
    // 创建 RustImageData (简化处理)
    ctx.set_text(format!("图片数据 ({} 字节)", image_bytes.len())).map_err(|e| e.to_string())?;
    Ok(())
}

#[tauri::command]
fn copy_files_to_clipboard(file_paths: Vec<String>) -> Result<(), String> {
    let ctx = ClipboardContext::new().map_err(|e| e.to_string())?;
    
    // 验证所有文件路径存在
    for path in &file_paths {
        if !std::path::Path::new(path).exists() {
            return Err(format!("文件不存在: {}", path));
        }
    }
    
    ctx.set_files(file_paths).map_err(|e| e.to_string())?;
    Ok(())
}

// 同步相关命令
#[tauri::command]
async fn setup_sync(
    _user_id: String,
    _storage_config: serde_json::Value,
    state: tauri::State<'_, ClipboardSyncContainer>
) -> Result<(), String> {
    // 重新初始化同步引擎（配置已经通过configure_storage保存了）
    let sync_engine = create_sync_engine_if_configured().await;
    
    if let Ok(mut container) = state.lock() {
        *container = sync_engine;
        Ok(())
    } else {
        Err("无法更新同步引擎".to_string())
    }
}

#[tauri::command]
async fn sync_now(state: tauri::State<'_, ClipboardSyncContainer>) -> Result<(), String> {
    let sync_engine_clone = {
        if let Ok(sync_engine_opt) = state.lock() {
            sync_engine_opt.clone()
        } else {
            return Err("无法获取同步引擎".to_string());
        }
    };
    
    if let Some(sync_engine) = sync_engine_clone {
        sync_engine.sync_now().await.map_err(|e| e.to_string())
    } else {
        Err("同步引擎未初始化".to_string())
    }
}

#[tauri::command]
async fn get_sync_status(state: tauri::State<'_, ClipboardSyncContainer>) -> Result<serde_json::Value, String> {
    let sync_engine_clone = {
        if let Ok(sync_engine_opt) = state.lock() {
            sync_engine_opt.clone()
        } else {
            return Err("无法获取同步引擎".to_string());
        }
    };
    
    if let Some(sync_engine) = sync_engine_clone {
        let status = sync_engine.get_status().await.map_err(|e| e.to_string())?;
        Ok(status)
    } else {
        // 返回未初始化状态
        Ok(serde_json::json!({
            "item_count": 0,
            "is_syncing": false,
            "initialized": false
        }))
    }
}

#[tauri::command]
async fn configure_storage(
    storage_config: serde_json::Value,
    app_handle: AppHandle
) -> Result<(), String> {
    // 验证存储配置
    let config: StorageConfig = serde_json::from_value(storage_config)
        .map_err(|e| format!("Invalid storage config: {}", e))?;
    
    // 验证配置有效性
    config.validate().await.map_err(|e| e.to_string())?;
    
    // 保存配置到用户配置目录
    let config_file = get_app_data_dir().join("storage_config.json");
    config.save_to_file(config_file.to_string_lossy().as_ref()).map_err(|e| e.to_string())?;
    
    // 重新初始化同步引擎
    reload_sync_engine(&app_handle).await?;
    
    Ok(())
}

/// 重新加载同步引擎
async fn reload_sync_engine(app_handle: &AppHandle) -> Result<(), String> {
    let sync_engine_container: tauri::State<ClipboardSyncContainer> = app_handle.state();
    let sync_engine = create_sync_engine_if_configured().await;
    
    {
        if let Ok(mut container) = sync_engine_container.lock() {
            *container = sync_engine;
            tracing::info!("同步引擎已重新加载");
        } else {
            return Err("无法更新同步引擎".to_string());
        }
    }
    
    Ok(())
}

#[tauri::command]
async fn get_storage_config() -> Result<serde_json::Value, String> {
    let config_file = get_app_data_dir().join("storage_config.json");
    match StorageConfig::load_from_file(config_file.to_string_lossy().as_ref()) {
        Ok(config) => {
            // 移除敏感信息后返回配置
            let mut config_value = serde_json::to_value(config).map_err(|e| e.to_string())?;
            
            // 隐藏敏感字段
            if let Some(backend) = config_value.get_mut("backend") {
                hide_sensitive_fields(backend);
            }
            
            Ok(config_value)
        }
        Err(_) => {
            // 返回默认配置
            let default_config = StorageConfig::default();
            let config_value = serde_json::to_value(default_config).map_err(|e| e.to_string())?;
            Ok(config_value)
        }
    }
}

#[tauri::command]
async fn test_storage_connection(storage_config: serde_json::Value) -> Result<String, String> {
    let config: StorageConfig = serde_json::from_value(storage_config)
        .map_err(|e| format!("Invalid storage config: {}", e))?;
    
    match config.validate().await {
        Ok(_) => Ok("连接成功！存储配置有效。".to_string()),
        Err(e) => Err(format!("连接失败: {}", e))
    }
}

#[tauri::command]
fn get_storage_backend_types() -> Vec<String> {
    vec![
        "FileSystem".to_string(),
        "S3".to_string(),
        "S3Compatible".to_string(),
        "Oss".to_string(),
        "Cos".to_string(),
        "AzBlob".to_string(),
    ]
}

// 辅助函数：隐藏敏感配置字段
fn hide_sensitive_fields(backend: &mut serde_json::Value) {
    match backend {
        serde_json::Value::Object(map) => {
            for (_, value) in map.iter_mut() {
                if let serde_json::Value::Object(config) = value {
                    // 隐藏密钥和密码字段
                    let sensitive_fields = [
                        "secret_access_key", "access_key_secret", 
                        "secret_key", "account_key"
                    ];
                    
                    for field in sensitive_fields {
                        if config.contains_key(field) {
                            config.insert(field.to_string(), serde_json::Value::String("***".to_string()));
                        }
                    }
                }
            }
        }
        _ => {}
    }
}

// 计算文件大小
fn calculate_files_size(file_paths: &[String]) -> u64 {
    file_paths.iter()
        .filter_map(|path| std::fs::metadata(path).ok())
        .map(|metadata| metadata.len())
        .sum()
}

// 剪切板管理器
struct ClipboardManager {
    ctx: ClipboardContext,
    app_handle: AppHandle,
    storage: ClipboardStorage,
    hybrid_storage: Option<HybridClipboardStorage>,
    sync_engine: Option<ClipboardSync>,
    hybrid_sync: Option<Arc<HybridSyncEngine>>,
    runtime_handle: tokio::runtime::Handle,
    last_text: String,
    last_files: Vec<String>,
}

impl ClipboardManager {
    pub fn new(
        app_handle: AppHandle, 
        storage: ClipboardStorage,
        hybrid_storage: Option<HybridClipboardStorage>,
        sync_engine: Option<ClipboardSync>,
        hybrid_sync: Option<Arc<HybridSyncEngine>>,
        runtime_handle: tokio::runtime::Handle
    ) -> Result<Self, String> {
        let ctx = ClipboardContext::new()
            .map_err(|e| format!("Failed to create clipboard context: {}", e))?;

        Ok(Self {
            ctx,
            app_handle,
            storage,
            hybrid_storage,
            sync_engine,
            hybrid_sync,
            runtime_handle,
            last_text: String::new(),
            last_files: Vec::new(),
        })
    }

    fn add_item_to_history(&self, item: ClipboardItem) {
        // 优先使用混合同步，其次混合存储，最后传统存储
        if let Some(ref hybrid_sync) = self.hybrid_sync {
            let hybrid_sync_clone = hybrid_sync.clone();
            let item_clone = item.clone();
            self.runtime_handle.spawn(async move {
                if let Err(e) = hybrid_sync_clone.add_item(&item_clone).await {
                    eprintln!("混合同步添加项目失败: {}", e);
                }
            });
        } else if let Some(ref hybrid_storage) = self.hybrid_storage {
            let hybrid_storage_clone = hybrid_storage.clone();
            let item_clone = item.clone();
            self.runtime_handle.spawn(async move {
                if let Err(e) = hybrid_storage_clone.add_item(item_clone).await {
                    eprintln!("混合存储剪切板项目失败: {}", e);
                }
            });
        } else {
            // 回退到传统存储
            if let Ok(mut storage_lock) = self.storage.lock() {
                if let Err(e) = storage_lock.insert(&item) {
                    eprintln!("存储剪切板项目失败: {}", e);
                }
            }
        }

        // 如果没有混合同步但有传统同步，也要添加到同步引擎
        if self.hybrid_sync.is_none() {
            if let Some(sync_engine) = &self.sync_engine {
                let sync_item = SyncClipboardItem::from(&item);
                let app_handle_clone = self.app_handle.clone();
                let sync_engine_clone = sync_engine.clone();
                
                // 使用运行时句柄来执行异步操作
                self.runtime_handle.spawn(async move {
                    if let Err(e) = sync_engine_clone.local_add(sync_item).await {
                        eprintln!("同步添加项目失败: {}", e);
                        // 可以发送错误事件到前端
                        let _ = app_handle_clone.emit("sync-error", format!("同步失败: {}", e));
                    }
                });
            }
        }
        
        // 发送事件到前端
        let _ = self.app_handle.emit("clipboard-update", &item);
    }

    fn check_text_change(&mut self) {
        if let Ok(text) = self.ctx.get_text() {
            if text != self.last_text && !text.trim().is_empty() {
                let item = ClipboardItem {
                    id: uuid::Uuid::new_v4().to_string(),
                    content: text.clone(),
                    timestamp: std::time::SystemTime::now()
                        .duration_since(std::time::UNIX_EPOCH)
                        .unwrap()
                        .as_secs(),
                    item_type: "text".to_string(),
                    size: Some(text.len() as u64),
                    file_paths: None,
                    file_types: None,
                };
                
                self.add_item_to_history(item);
                self.last_text = text;
            }
        }
    }

    fn check_files_change(&mut self) {
        if let Ok(files) = self.ctx.get_files() {
            if files != self.last_files && !files.is_empty() {
                let total_size = calculate_files_size(&files);
                
                // 检测文件类型
                let file_types: Vec<FileTypeInfo> = files.iter()
                    .map(|file_path| detect_file_type(file_path))
                    .collect();
                
                let item = ClipboardItem {
                    id: uuid::Uuid::new_v4().to_string(),
                    content: format!("{} 个文件", files.len()),
                    timestamp: std::time::SystemTime::now()
                        .duration_since(std::time::UNIX_EPOCH)
                        .unwrap()
                        .as_secs(),
                    item_type: "files".to_string(),
                    size: Some(total_size),
                    file_paths: Some(files.clone()),
                    file_types: Some(file_types),
                };
                
                self.add_item_to_history(item);
                self.last_files = files;
            }
        }
    }
}

impl ClipboardHandler for ClipboardManager {
    fn on_clipboard_change(&mut self) {
        // 简化逻辑：只区分文件和文本
        
        // 首先检查是否有文件
        if self.ctx.has(ContentFormat::Files) {
            self.check_files_change();
            return;
        }
        
        // 然后检查文本
        if self.ctx.has(ContentFormat::Text) {
            self.check_text_change();
        }
    }
}

// 启动剪贴板监听器
fn start_clipboard_monitor(app_handle: AppHandle, storage: ClipboardStorage, sync_engine_container: Arc<Mutex<Option<ClipboardSync>>>, hybrid_sync_container: Arc<Mutex<Option<Arc<HybridSyncEngine>>>>) {
    thread::spawn(move || {
        let rt = tokio::runtime::Runtime::new().unwrap();
        
        // 先异步初始化传统同步引擎（作为备用）
        let sync_engine = rt.block_on(async {
            create_sync_engine_if_configured().await
        });
        
        // 更新传统同步引擎容器
        if let Some(ref engine) = sync_engine {
            if let Ok(mut container) = sync_engine_container.lock() {
                *container = Some(engine.clone());
                tracing::info!("传统同步引擎初始化成功");
            }
        }

        // 等待一小段时间让混合同步引擎初始化
        std::thread::sleep(std::time::Duration::from_millis(500));

        // 检查是否有混合同步引擎可用
        let hybrid_sync = {
            if let Ok(container) = hybrid_sync_container.lock() {
                container.clone()
            } else {
                None
            }
        };

        let manager = match ClipboardManager::new(
            app_handle.clone(), 
            storage, 
            None, // hybrid_storage 已经在混合同步中管理
            sync_engine, 
            hybrid_sync,
            rt.handle().clone()
        ) {
            Ok(manager) => manager,
            Err(e) => {
                eprintln!("Failed to create clipboard manager: {}", e);
                return;
            }
        };

        let mut watcher = match ClipboardWatcherContext::new() {
            Ok(watcher) => watcher,
            Err(e) => {
                eprintln!("Failed to create clipboard watcher: {}", e);
                return;
            }
        };

        println!("开始监听剪切板变化...");
        
        // 添加处理器并开始监听
        watcher.add_handler(manager);
        watcher.start_watch();
    });
}

// 检测文件类型的辅助函数
fn detect_file_type(file_path: &str) -> FileTypeInfo {
    let path = Path::new(file_path);
    let extension = path.extension()
        .and_then(|ext| ext.to_str())
        .unwrap_or("")
        .to_lowercase();
    
    // 尝试从文件内容检测 MIME 类型
    let mime_type = if let Ok(bytes) = std::fs::read(file_path) {
        if let Some(kind) = infer::get(&bytes) {
            kind.mime_type().to_string()
        } else {
            guess_mime_by_extension(&extension)
        }
    } else {
        guess_mime_by_extension(&extension)
    };
    
    // 根据扩展名或 MIME 类型确定类别
    let category = categorize_file(&extension, &mime_type);
    
    FileTypeInfo {
        path: file_path.to_string(),
        file_type: extension,
        mime_type,
        category,
    }
}

// 根据扩展名猜测 MIME 类型
fn guess_mime_by_extension(extension: &str) -> String {
    match extension {
        "txt" => "text/plain",
        "pdf" => "application/pdf",
        "doc" | "docx" => "application/msword",
        "xls" | "xlsx" => "application/vnd.ms-excel",
        "ppt" | "pptx" => "application/vnd.ms-powerpoint",
        "zip" => "application/zip",
        "rar" => "application/x-rar-compressed",
        "7z" => "application/x-7z-compressed",
        "jpg" | "jpeg" => "image/jpeg",
        "png" => "image/png",
        "gif" => "image/gif",
        "mp4" => "video/mp4",
        "mp3" => "audio/mpeg",
        "wav" => "audio/wav",
        "html" | "htm" => "text/html",
        "css" => "text/css",
        "js" => "application/javascript",
        "json" => "application/json",
        "xml" => "application/xml",
        _ => "application/octet-stream",
    }.to_string()
}

// 文件分类
fn categorize_file(extension: &str, mime_type: &str) -> String {
    if mime_type.starts_with("image/") {
        "image".to_string()
    } else if mime_type.starts_with("video/") {
        "video".to_string()
    } else if mime_type.starts_with("audio/") {
        "audio".to_string()
    } else if mime_type.starts_with("text/") || matches!(extension, "txt" | "md" | "csv" | "log") {
        "text".to_string()
    } else if matches!(extension, "pdf" | "doc" | "docx" | "xls" | "xlsx" | "ppt" | "pptx") {
        "document".to_string()
    } else if matches!(extension, "zip" | "rar" | "7z" | "tar" | "gz" | "bz2") {
        "archive".to_string()
    } else if matches!(extension, "js" | "ts" | "py" | "java" | "cpp" | "c" | "h" | "rs" | "go" | "php" | "rb" | "swift") {
        "code".to_string()
    } else {
        "other".to_string()
    }
}

/// 获取应用配置目录，跨平台适配
fn get_app_data_dir() -> PathBuf {
    // 尝试获取用户配置目录
    if let Some(config_dir) = dirs::config_dir() {
        config_dir.join("clippy")
    } else {
        // 如果无法获取配置目录，使用用户主目录
        if let Some(home_dir) = dirs::home_dir() {
            home_dir.join(".clippy")
        } else {
            // 最后的回退：使用当前目录
            PathBuf::from("./clippy_data")
        }
    }
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    // 初始化日志
    tracing_subscriber::fmt::init();
    
    // 获取设备ID
    let device_id = get_or_create_device_id();
    
    // 创建存储引擎 - 使用用户配置目录而不是项目目录
    let storage_dir = get_app_data_dir();
    
    // 创建一个新的 tokio runtime 来执行异步初始化
    let rt = tokio::runtime::Runtime::new().expect("Failed to create runtime");
    let storage_engine = rt.block_on(async {
        match StorageEngine::new(storage_dir, device_id.clone()).await {
            Ok(engine) => engine,
            Err(e) => {
                eprintln!("创建存储引擎失败: {}", e);
                panic!("无法初始化存储");
            }
        }
    });
    
    // 暂时创建一个空的旧存储引擎用于兼容性
    let old_storage_engine = storage::StorageEngine::new(&storage_dir);
    let clipboard_storage: ClipboardStorage = Arc::new(Mutex::new(old_storage_engine));

    // 创建混合存储引擎（异步初始化）
    let _hybrid_storage: Option<HybridClipboardStorage> = None; // 将在 setup 中异步初始化

    // 创建同步引擎的状态容器
    let sync_engine: Arc<Mutex<Option<ClipboardSync>>> = Arc::new(Mutex::new(None));

    // 创建混合同步引擎的状态容器
    let hybrid_sync_container: HybridSyncContainer = Arc::new(Mutex::new(None));

    tauri::Builder::default()
        .plugin(tauri_plugin_opener::init())
        .plugin(tauri_plugin_shell::init())
        .manage(clipboard_storage.clone())
        .manage(sync_engine.clone())
        .manage(hybrid_sync_container.clone())
        .manage(NewSyncEngineContainer::new(Mutex::new(None)))
        .setup(move |app| {
            // 异步初始化混合存储和同步引擎
            let app_handle = app.handle().clone();
            let storage_dir = get_app_data_dir();
            let device_id = get_or_create_device_id();
            let hybrid_sync_container_clone = hybrid_sync_container.clone();
            
            tauri::async_runtime::spawn(async move {
                // 初始化混合存储引擎
                match HybridStorageEngine::new(storage_dir, device_id).await {
                    Ok(hybrid_engine) => {
                        let hybrid_storage: HybridClipboardStorage = Arc::new(hybrid_engine);
                        app_handle.manage(hybrid_storage.clone());
                        tracing::info!("混合存储引擎初始化成功");

                        // 尝试创建混合同步引擎
                        if let Some(cloud_sync) = create_sync_engine_if_configured().await {
                            let hybrid_sync = Arc::new(HybridSyncEngine::new(
                                hybrid_storage.clone(),
                                cloud_sync
                            ));

                            // 初始化混合同步
                            if let Err(e) = hybrid_sync.initialize().await {
                                tracing::error!("混合同步引擎初始化失败: {}", e);
                            } else {
                                tracing::info!("混合同步引擎初始化成功");

                                // 启动后台同步任务
                                let hybrid_sync_clone = hybrid_sync.clone();
                                tokio::spawn(async move {
                                    if let Err(e) = hybrid_sync_clone.start_background_sync(15).await {
                                        tracing::error!("混合同步后台任务失败: {}", e);
                                    }
                                });

                                // 保存到全局状态
                                if let Ok(mut container) = hybrid_sync_container_clone.lock() {
                                    *container = Some(hybrid_sync);
                                }
                            }
                        } else {
                            tracing::info!("未配置云同步，只使用混合存储");
                        }
                    }
                    Err(e) => {
                        tracing::error!("混合存储引擎初始化失败: {}", e);
                    }
                }
            });

            // 创建系统托盘
            let _tray = TrayIconBuilder::new()
                .icon(app.default_window_icon().unwrap().clone())
                .title("Clippy - 剪贴板管理器")
                .tooltip("Clippy - 剪贴板管理器")
                .on_tray_icon_event(|tray, event| {
                    if let TrayIconEvent::Click { .. } = event {
                        if let Some(window) = tray.app_handle().get_webview_window("main") {
                            let _ = window.show();
                            let _ = window.set_focus();
                        }
                    }
                })
                .build(app)?;

            // 启动剪贴板监听器（带同步引擎初始化）
            start_clipboard_monitor(app.handle().clone(), clipboard_storage, sync_engine, hybrid_sync_container);

            Ok(())
        })
        .invoke_handler(tauri::generate_handler![
            get_clipboard_history,
            clear_clipboard_history,
            delete_clipboard_item,
            get_storage_stats,
            compact_storage,
            copy_to_clipboard,
            copy_image_to_clipboard,
            copy_files_to_clipboard,
            setup_sync,
            sync_now,
            get_sync_status,
            configure_storage,
            get_storage_config,
            test_storage_connection,
            get_storage_backend_types,
            // 混合存储命令
            hybrid_get_clipboard_history,
            hybrid_get_clipboard_history_paginated,
            hybrid_search_clipboard,
            hybrid_get_storage_stats,
            hybrid_clear_clipboard_history,
            hybrid_vacuum_storage,
            // 混合同步相关命令
            hybrid_sync_now,
            hybrid_sync_status,
            hybrid_sync_delete_item,
            hybrid_sync_clear_all,
            hybrid_sync_create_snapshot,
            // 新的 Tauri 命令
            new_add_text,
            new_add_image,
            new_add_file,
            new_get_all_items,
            new_delete_item,
            new_sync_now,
            new_get_stats,
            new_get_sync_status,
            new_clear_all,
            new_create_snapshot
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}

/// 如果存在配置，创建同步引擎
async fn create_sync_engine_if_configured() -> Option<ClipboardSync> {
    // 尝试从配置文件加载同步配置
    let config_file = get_app_data_dir().join("storage_config.json");
    if let Ok(storage_config) = StorageConfig::load_from_file(config_file.to_string_lossy().as_ref()) {
        if let Ok(operator) = storage_config.create_operator().await {
            // 生成设备ID（应该持久化存储）
            let device_id = get_or_create_device_id();
            
            // 这里应该从用户配置获取user_id，暂时使用默认值
            let user_id = std::env::var("CLIPPY_USER_ID").unwrap_or_else(|_| "default_user".to_string());
            
            let sync_config = SyncConfig {
                user_id,
                device_id,
                storage_operator: operator,
                sync_interval_seconds: 15, // 15秒同步一次
            };
            
            let sync_engine = Arc::new(SyncEngine::new(sync_config));
            
            // 启动后台同步任务
            let sync_engine_clone = sync_engine.clone();
            tokio::spawn(async move {
                if let Err(e) = sync_engine_clone.start_background_sync().await {
                    tracing::error!("后台同步任务失败: {}", e);
                }
            });
            
            return Some(sync_engine);
        }
    }
    
    None
}

/// 获取或创建设备唯一ID
fn get_or_create_device_id() -> String {
    let device_id_file = get_app_data_dir().join("device_id");
    
    // 尝试从文件读取设备ID
    if let Ok(device_id) = std::fs::read_to_string(&device_id_file) {
        let device_id = device_id.trim();
        if !device_id.is_empty() {
            return device_id.to_string();
        }
    }
    
    // 生成新的设备ID
    let device_id = uuid::Uuid::new_v4().to_string();
    
    // 确保目录存在
    if let Some(parent) = device_id_file.parent() {
        let _ = std::fs::create_dir_all(parent);
    }
    
    // 保存到文件
    if let Err(e) = std::fs::write(&device_id_file, &device_id) {
        eprintln!("保存设备ID失败: {}", e);
    }
    
    device_id
}

// 混合同步相关命令
#[tauri::command]
async fn hybrid_sync_now(state: tauri::State<'_, HybridSyncContainer>) -> Result<(), String> {
    let hybrid_sync_clone = {
        if let Ok(hybrid_sync_opt) = state.lock() {
            hybrid_sync_opt.clone()
        } else {
            return Err("无法获取混合同步引擎".to_string());
        }
    };
    
    if let Some(hybrid_sync) = hybrid_sync_clone {
        hybrid_sync.sync().await.map_err(|e| e.to_string())
    } else {
        Err("混合同步引擎未初始化".to_string())
    }
}

#[tauri::command]
async fn hybrid_sync_status(state: tauri::State<'_, HybridSyncContainer>) -> Result<serde_json::Value, String> {
    let hybrid_sync_clone = {
        if let Ok(hybrid_sync_opt) = state.lock() {
            hybrid_sync_opt.clone()
        } else {
            return Err("无法获取混合同步引擎".to_string());
        }
    };
    
    if let Some(hybrid_sync) = hybrid_sync_clone {
        hybrid_sync.get_sync_status().await.map_err(|e| e.to_string())
    } else {
        // 返回未初始化状态
        Ok(serde_json::json!({
            "is_hybrid_enabled": false,
            "initialized": false
        }))
    }
}

#[tauri::command]
async fn hybrid_sync_delete_item(item_id: String, state: tauri::State<'_, HybridSyncContainer>) -> Result<bool, String> {
    let hybrid_sync_clone = {
        if let Ok(hybrid_sync_opt) = state.lock() {
            hybrid_sync_opt.clone()
        } else {
            return Err("无法获取混合同步引擎".to_string());
        }
    };
    
    if let Some(hybrid_sync) = hybrid_sync_clone {
        hybrid_sync.delete_item(&item_id).await.map_err(|e| e.to_string())
    } else {
        Err("混合同步引擎未初始化".to_string())
    }
}

#[tauri::command]
async fn hybrid_sync_clear_all(state: tauri::State<'_, HybridSyncContainer>) -> Result<(), String> {
    let hybrid_sync_clone = {
        if let Ok(hybrid_sync_opt) = state.lock() {
            hybrid_sync_opt.clone()
        } else {
            return Err("无法获取混合同步引擎".to_string());
        }
    };
    
    if let Some(hybrid_sync) = hybrid_sync_clone {
        hybrid_sync.clear_all().await.map_err(|e| e.to_string())
    } else {
        Err("混合同步引擎未初始化".to_string())
    }
}

#[tauri::command]
async fn hybrid_sync_create_snapshot(state: tauri::State<'_, HybridSyncContainer>) -> Result<(), String> {
    let hybrid_sync_clone = {
        if let Ok(hybrid_sync_opt) = state.lock() {
            hybrid_sync_opt.clone()
        } else {
            return Err("无法获取混合同步引擎".to_string());
        }
    };
    
    if let Some(hybrid_sync) = hybrid_sync_clone {
        hybrid_sync.create_snapshot().await.map_err(|e| e.to_string())
    } else {
        Err("混合同步引擎未初始化".to_string())
    }
}

// 新的 Tauri 命令
#[tauri::command]
async fn new_add_text(
    text: String,
    state: tauri::State<'_, NewSyncEngineContainer>
) -> Result<(), String> {
    if let Ok(container) = state.lock() {
        if let Some(ref engine) = *container {
            engine.add_text(&text).await.map_err(|e| e.to_string())?;
            Ok(())
        } else {
            Err("新同步引擎未初始化".to_string())
        }
    } else {
        Err("无法获取同步引擎".to_string())
    }
}

#[tauri::command]
async fn new_add_image(
    base64_data: String,
    format: String,
    state: tauri::State<'_, NewSyncEngineContainer>
) -> Result<(), String> {
    if let Ok(container) = state.lock() {
        if let Some(ref engine) = *container {
            // 解码 base64 数据
            let image_data = general_purpose::STANDARD
                .decode(base64_data)
                .map_err(|e| format!("解码 base64 失败: {}", e))?;
            
            engine.add_image(&image_data, &format).await.map_err(|e| e.to_string())?;
            Ok(())
        } else {
            Err("新同步引擎未初始化".to_string())
        }
    } else {
        Err("无法获取同步引擎".to_string())
    }
}

#[tauri::command]
async fn new_add_file(
    file_path: String,
    state: tauri::State<'_, NewSyncEngineContainer>
) -> Result<(), String> {
    if let Ok(container) = state.lock() {
        if let Some(ref engine) = *container {
            engine.add_file(&file_path).await.map_err(|e| e.to_string())?;
            Ok(())
        } else {
            Err("新同步引擎未初始化".to_string())
        }
    } else {
        Err("无法获取同步引擎".to_string())
    }
}

#[tauri::command]
async fn new_get_all_items(
    state: tauri::State<'_, NewSyncEngineContainer>
) -> Result<Vec<models::ClipboardItem>, String> {
    if let Ok(container) = state.lock() {
        if let Some(ref engine) = *container {
            engine.get_all_items().await.map_err(|e| e.to_string())
        } else {
            Err("新同步引擎未初始化".to_string())
        }
    } else {
        Err("无法获取同步引擎".to_string())
    }
}

#[tauri::command]
async fn new_delete_item(
    item_id: String,
    state: tauri::State<'_, NewSyncEngineContainer>
) -> Result<bool, String> {
    if let Ok(container) = state.lock() {
        if let Some(ref engine) = *container {
            engine.delete_item(&item_id).await.map_err(|e| e.to_string())
        } else {
            Err("新同步引擎未初始化".to_string())
        }
    } else {
        Err("无法获取同步引擎".to_string())
    }
}

#[tauri::command]
async fn new_sync_now(
    state: tauri::State<'_, NewSyncEngineContainer>
) -> Result<(), String> {
    if let Ok(container) = state.lock() {
        if let Some(ref engine) = *container {
            engine.sync().await.map_err(|e| e.to_string())
        } else {
            Err("新同步引擎未初始化".to_string())
        }
    } else {
        Err("无法获取同步引擎".to_string())
    }
}

#[tauri::command]
async fn new_get_stats(
    state: tauri::State<'_, NewSyncEngineContainer>
) -> Result<models::StorageStats, String> {
    if let Ok(container) = state.lock() {
        if let Some(ref engine) = *container {
            engine.get_stats().await.map_err(|e| e.to_string())
        } else {
            Err("新同步引擎未初始化".to_string())
        }
    } else {
        Err("无法获取同步引擎".to_string())
    }
}

#[tauri::command]
async fn new_get_sync_status(
    state: tauri::State<'_, NewSyncEngineContainer>
) -> Result<serde_json::Value, String> {
    if let Ok(container) = state.lock() {
        if let Some(ref engine) = *container {
            engine.get_sync_status().await.map_err(|e| e.to_string())
        } else {
            Err("新同步引擎未初始化".to_string())
        }
    } else {
        Err("无法获取同步引擎".to_string())
    }
}

#[tauri::command]
async fn new_clear_all(
    state: tauri::State<'_, NewSyncEngineContainer>
) -> Result<(), String> {
    if let Ok(container) = state.lock() {
        if let Some(ref engine) = *container {
            engine.clear_all().await.map_err(|e| e.to_string())
        } else {
            Err("新同步引擎未初始化".to_string())
        }
    } else {
        Err("无法获取同步引擎".to_string())
    }
}

#[tauri::command]
async fn new_create_snapshot(
    state: tauri::State<'_, NewSyncEngineContainer>
) -> Result<(), String> {
    if let Ok(container) = state.lock() {
        if let Some(ref engine) = *container {
            engine.create_snapshot().await.map_err(|e| e.to_string())
        } else {
            Err("新同步引擎未初始化".to_string())
        }
    } else {
        Err("无法获取同步引擎".to_string())
    }
}
