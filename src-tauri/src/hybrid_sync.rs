use anyhow::Result;
use chrono::{DateTime, Utc};
use std::sync::Arc;
use tokio::sync::RwLock;
use serde::{Deserialize, Serialize};

use crate::hybrid_storage::HybridStorageEngine;
use crate::storage::ClipboardItem;
use crate::sync::{SyncEngine, SyncClipboardItem, ItemMetadata};

/// 数据快照结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DataSnapshot {
    pub snapshot_id: String,
    pub created_at: DateTime<Utc>,
    pub device_id: String,
    pub items: Vec<ClipboardItem>,
    pub total_items: usize,
    pub snapshot_version: u32,
}

/// 快照元数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SnapshotMetadata {
    pub latest_snapshot_id: String,
    pub created_at: DateTime<Utc>,
    pub total_items: usize,
    pub snapshot_version: u32,
    pub device_id: String,
}

/// 混合同步引擎
/// 结合SQLite全量存储和云端增量同步
pub struct HybridSyncEngine {
    hybrid_storage: Arc<HybridStorageEngine>,
    cloud_sync: Arc<SyncEngine>,
    last_sync_timestamp: RwLock<Option<DateTime<Utc>>>,
    last_snapshot_timestamp: RwLock<Option<DateTime<Utc>>>,
}

impl HybridSyncEngine {
    /// 创建新的混合同步引擎
    pub fn new(
        hybrid_storage: Arc<HybridStorageEngine>,
        cloud_sync: Arc<SyncEngine>,
    ) -> Self {
        Self {
            hybrid_storage,
            cloud_sync,
            last_sync_timestamp: RwLock::new(None),
            last_snapshot_timestamp: RwLock::new(None),
        }
    }

    /// 初始化同步（支持全量快照恢复）
    /// 1. 检查云端是否有快照
    /// 2. 如果有快照且本地为空，则下载快照
    /// 3. 应用快照后的增量操作
    /// 4. 将本地数据同步到云端状态
    pub async fn initialize(&self) -> Result<()> {
        tracing::info!("初始化混合同步引擎...");

        // 检查本地是否有数据
        let local_items = self.hybrid_storage.get_all_for_sync().await?;
        let local_item_count = local_items.len();
        
        // 检查云端快照
        let latest_snapshot = self.get_latest_snapshot_metadata().await?;
        
        if local_item_count == 0 && latest_snapshot.is_some() {
            // 本地为空但云端有快照，下载并应用快照
            tracing::info!("检测到本地为空但云端有快照，正在下载全量数据...");
            self.restore_from_snapshot().await?;
            tracing::info!("快照恢复完成");
        } else {
            // 将本地现有数据同步到云端状态
            tracing::info!("从混合存储加载了 {} 个项目", local_item_count);
            for item in local_items {
                let sync_item = self.clipboard_item_to_sync_item(&item);
                if let Err(e) = self.cloud_sync.local_add(sync_item).await {
                    tracing::warn!("初始化时同步项目失败: {}", e);
                }
            }
        }

        // 执行首次云端同步（获取增量更新）
        if let Err(e) = self.cloud_sync.sync_now().await {
            tracing::warn!("首次云端同步失败: {}", e);
        }

        // 同步云端增量到本地
        self.apply_cloud_changes().await?;

        tracing::info!("混合同步引擎初始化完成");
        Ok(())
    }

    /// 添加新的剪切板项目
    /// 同时写入混合存储和云端同步
    pub async fn add_item(&self, item: &ClipboardItem) -> Result<()> {
        // 首先写入混合存储
        self.hybrid_storage.insert(item).await?;

        // 然后同步到云端
        let sync_item = self.clipboard_item_to_sync_item(item);
        self.cloud_sync.local_add(sync_item).await?;

        Ok(())
    }

    /// 删除剪切板项目
    /// 同时从混合存储和云端删除
    pub async fn delete_item(&self, item_id: &str) -> Result<bool> {
        // 从混合存储软删除
        let deleted = self.hybrid_storage.sync_delete(item_id).await?;

        if deleted {
            // 从云端删除
            if let Err(e) = self.cloud_sync.local_delete(item_id.to_string()).await {
                tracing::warn!("云端删除项目失败: {}", e);
            } else {
                // 删除成功后立即同步到云端
                tracing::info!("项目删除成功，立即同步到云端: {}", item_id);
                if let Err(e) = self.cloud_sync.sync_now().await {
                    tracing::warn!("删除后同步失败: {}", e);
                }
            }
        }

        Ok(deleted)
    }

    /// 执行同步
    /// 将云端的变更下载并应用到混合存储
    pub async fn sync(&self) -> Result<()> {
        tracing::debug!("开始混合同步...");

        // 执行云端同步
        self.cloud_sync.sync_now().await?;

        // 应用云端变更到本地
        self.apply_cloud_changes().await?;

        // 检查是否需要创建新快照
        self.check_and_create_snapshot().await?;

        // 更新同步时间戳
        {
            let mut timestamp = self.last_sync_timestamp.write().await;
            *timestamp = Some(Utc::now());
        }

        tracing::debug!("混合同步完成");
        Ok(())
    }

    /// 应用云端变更到本地存储
    async fn apply_cloud_changes(&self) -> Result<()> {
        // 获取云端的所有项目
        let cloud_items = self.cloud_sync.get_all_items().await;
        
        // 转换为本地格式并应用到混合存储
        let local_items: Vec<ClipboardItem> = cloud_items
            .iter()
            .map(|sync_item| self.sync_item_to_clipboard_item(sync_item))
            .collect();

        if !local_items.is_empty() {
            let applied_count = self.hybrid_storage.apply_sync_batch(local_items).await?;
            if applied_count > 0 {
                tracing::info!("应用了 {} 个云端变更", applied_count);
            }
        }

        Ok(())
    }

    /// 获取最新快照元数据
    async fn get_latest_snapshot_metadata(&self) -> Result<Option<SnapshotMetadata>> {
        // 构建快照元数据路径
        let metadata_path = format!("snapshots/latest_snapshot.json");
        
        match self.cloud_sync.storage_operator().read(&metadata_path).await {
            Ok(data) => {
                let bytes = data.to_vec();
                let metadata: SnapshotMetadata = serde_json::from_slice(&bytes)?;
                Ok(Some(metadata))
            }
            Err(_) => {
                // 快照不存在
                Ok(None)
            }
        }
    }

    /// 从快照恢复数据
    async fn restore_from_snapshot(&self) -> Result<()> {
        if let Some(metadata) = self.get_latest_snapshot_metadata().await? {
            let snapshot_path = format!("snapshots/{}.json", metadata.latest_snapshot_id);
            
            match self.cloud_sync.storage_operator().read(&snapshot_path).await {
                Ok(data) => {
                    let bytes = data.to_vec();
                    let snapshot: DataSnapshot = serde_json::from_slice(&bytes)?;
                    
                    // 应用快照数据到本地存储
                    let applied_count = self.hybrid_storage.apply_sync_batch(snapshot.items).await?;
                    
                    tracing::info!("从快照恢复了 {} 个项目", applied_count);
                    
                    // 更新快照时间戳
                    {
                        let mut timestamp = self.last_snapshot_timestamp.write().await;
                        *timestamp = Some(snapshot.created_at);
                    }
                }
                Err(e) => {
                    tracing::warn!("下载快照失败: {}", e);
                }
            }
        }
        Ok(())
    }

    /// 检查并创建新快照（智能后台快照）
    async fn check_and_create_snapshot(&self) -> Result<()> {
        let should_create_snapshot = {
            let last_snapshot = self.last_snapshot_timestamp.read().await;
            let stats = self.hybrid_storage.get_stats().await?;
            
            match *last_snapshot {
                Some(last_time) => {
                    let elapsed = Utc::now() - last_time;
                    // 动态快照策略：
                    // 1. 超过2小时强制创建
                    // 2. 超过30分钟且有50+新增操作
                    // 3. 超过15分钟且有100+新增操作
                    elapsed.num_hours() >= 2 ||
                    (elapsed.num_minutes() >= 30 && stats.pending_ops >= 50) ||
                    (elapsed.num_minutes() >= 15 && stats.pending_ops >= 100)
                }
                None => {
                    // 从未创建过快照，检查是否有足够数据
                    stats.total_items >= 5 // 降低阈值，更积极地创建首次快照
                }
            }
        };

        if should_create_snapshot {
            tracing::info!("智能快照触发 - pending_ops: {}, elapsed: {:?}", 
                         self.hybrid_storage.get_stats().await?.pending_ops,
                         self.last_snapshot_timestamp.read().await);
            self.create_snapshot_with_conflict_resolution().await?;
        }

        Ok(())
    }

    /// 创建数据快照（带多端冲突解决）
    async fn create_snapshot_with_conflict_resolution(&self) -> Result<()> {
        let snapshot_id = uuid::Uuid::new_v4().to_string();
        let created_at = Utc::now();
        let device_id = self.hybrid_storage.get_device_id().to_string();
        
        // 检查是否有其他设备最近创建的快照
        if let Some(latest_metadata) = self.get_latest_snapshot_metadata().await? {
            let time_since_latest = created_at - latest_metadata.created_at;
            
            // 如果其他设备在10分钟内创建了快照，检查是否需要避免冲突
            if time_since_latest.num_minutes() < 10 && latest_metadata.device_id != device_id {
                let local_stats = self.hybrid_storage.get_stats().await?;
                
                // 如果本地没有显著更多的数据，跳过快照创建
                if local_stats.total_items <= (latest_metadata.total_items + 20) as i64 {
                    tracing::info!("跳过快照创建：其他设备({})最近已创建快照", 
                                 latest_metadata.device_id);
                    
                    // 更新本地时间戳以避免频繁检查
                    {
                        let mut timestamp = self.last_snapshot_timestamp.write().await;
                        *timestamp = Some(latest_metadata.created_at);
                    }
                    return Ok(());
                }
            }
        }
        
        // 获取所有活跃数据
        let items = self.hybrid_storage.get_all().await?;
        
        let snapshot = DataSnapshot {
            snapshot_id: snapshot_id.clone(),
            created_at,
            device_id: device_id.clone(),
            total_items: items.len(),
            items,
            snapshot_version: 1,
        };

        // 上传快照数据
        let snapshot_path = format!("snapshots/{}.json", snapshot_id);
        let snapshot_data = serde_json::to_vec(&snapshot)?;
        
        self.cloud_sync.storage_operator()
            .write(&snapshot_path, snapshot_data)
            .await?;

        // 原子性更新元数据（重新检查冲突）
        match self.update_snapshot_metadata_atomically(snapshot_id.clone(), created_at, snapshot.total_items, device_id).await {
            Ok(_) => {
                // 更新本地快照时间戳
                {
                    let mut timestamp = self.last_snapshot_timestamp.write().await;
                    *timestamp = Some(created_at);
                }
                
                tracing::info!("快照创建完成: {} ({} 个项目)", snapshot_id, snapshot.total_items);
                
                // 清理旧的快照（保留最近5个）
                self.cleanup_old_snapshots().await?;
            }
            Err(e) => {
                tracing::warn!("快照元数据更新失败（可能的并发冲突）: {}", e);
                // 删除刚创建的快照文件
                let _ = self.cloud_sync.storage_operator().delete(&snapshot_path).await;
            }
        }
        
        Ok(())
    }

    /// 原子性更新快照元数据
    async fn update_snapshot_metadata_atomically(
        &self, 
        snapshot_id: String, 
        created_at: DateTime<Utc>, 
        total_items: usize, 
        device_id: String
    ) -> Result<()> {
        // 再次检查最新元数据以确保原子性
        if let Some(current_metadata) = self.get_latest_snapshot_metadata().await? {
            // 如果发现更新的快照，放弃更新
            if current_metadata.created_at > created_at {
                return Err(anyhow::anyhow!("发现更新的快照，放弃当前更新"));
            }
        }
        
        let metadata = SnapshotMetadata {
            latest_snapshot_id: snapshot_id,
            created_at,
            total_items,
            snapshot_version: 1,
            device_id,
        };

        let metadata_path = "snapshots/latest_snapshot.json";
        let metadata_data = serde_json::to_vec(&metadata)?;
        
        self.cloud_sync.storage_operator()
            .write(&metadata_path, metadata_data)
            .await?;
        
        Ok(())
    }

    /// 清理旧快照
    async fn cleanup_old_snapshots(&self) -> Result<()> {
        // 这里可以实现清理逻辑，保留最近的几个快照
        // 为了简化，暂时只记录日志
        tracing::debug!("快照清理逻辑待实现");
        Ok(())
    }

    /// 创建数据快照（保留公开接口用于手动调用）
    pub async fn create_snapshot(&self) -> Result<()> {
        self.create_snapshot_with_conflict_resolution().await
    }

    /// 获取同步状态
    pub async fn get_sync_status(&self) -> Result<serde_json::Value> {
        let cloud_status = self.cloud_sync.get_status().await?;
        let hybrid_stats = self.hybrid_storage.get_stats().await?;
        let last_sync = self.last_sync_timestamp.read().await.clone();
        let last_snapshot = self.last_snapshot_timestamp.read().await.clone();

        Ok(serde_json::json!({
            "hybrid_storage": {
                "total_items": hybrid_stats.total_items,
                "active_items": hybrid_stats.active_items,
                "deleted_items": hybrid_stats.deleted_items,
                "pending_ops": hybrid_stats.pending_ops,
                "database_size": hybrid_stats.database_size,
                "oplog_size": hybrid_stats.oplog_size
            },
            "cloud_sync": cloud_status,
            "last_sync": last_sync,
            "last_snapshot": last_snapshot,
            "is_hybrid_enabled": true
        }))
    }

    /// 清空所有数据
    pub async fn clear_all(&self) -> Result<()> {
        // 清空混合存储
        self.hybrid_storage.clear_all().await?;

        // 获取所有项目ID并创建删除操作
        let all_items = self.cloud_sync.get_all_items().await;
        for item in all_items {
            if let Err(e) = self.cloud_sync.local_delete(item.id).await {
                tracing::warn!("删除云端项目失败: {}", e);
            }
        }

        // 立即同步删除操作到云端
        if let Err(e) = self.cloud_sync.sync_now().await {
            tracing::warn!("清空后同步失败: {}", e);
        } else {
            tracing::info!("已清空所有数据并同步到云端");
        }

        Ok(())
    }

    /// 获取所有剪切板项目
    pub async fn get_all_items(&self) -> Result<Vec<ClipboardItem>> {
        self.hybrid_storage.get_all().await
    }

    /// 搜索剪切板项目
    pub async fn search_items(&self, query: &str, limit: i64) -> Result<Vec<ClipboardItem>> {
        self.hybrid_storage.search(query, limit).await
    }

    /// 启动后台同步任务（包含智能快照）
    pub async fn start_background_sync(&self, interval_seconds: u64) -> Result<()> {
        let sync_interval = tokio::time::Duration::from_secs(interval_seconds);
        let snapshot_check_interval = tokio::time::Duration::from_secs(300); // 每5分钟检查快照
        
        let mut sync_timer = tokio::time::interval(sync_interval);
        let mut snapshot_timer = tokio::time::interval(snapshot_check_interval);

        loop {
            tokio::select! {
                _ = sync_timer.tick() => {
                    if let Err(e) = self.sync().await {
                        tracing::error!("混合同步失败: {}", e);
                    }
                }
                _ = snapshot_timer.tick() => {
                    if let Err(e) = self.check_and_create_snapshot().await {
                        tracing::error!("智能快照检查失败: {}", e);
                    }
                }
            }
        }
    }

    /// 将ClipboardItem转换为SyncClipboardItem
    fn clipboard_item_to_sync_item(&self, item: &ClipboardItem) -> SyncClipboardItem {
        SyncClipboardItem {
            id: item.id.clone(),
            content_type: item.item_type.clone(),
            content: item.content.clone(),
            created_at: DateTime::from_timestamp(item.timestamp as i64, 0)
                .unwrap_or_else(Utc::now),
            metadata: ItemMetadata {
                source_device: self.hybrid_storage.device_id().to_string(),
                source_app: None,
                content_hash: None,
            },
        }
    }

    /// 将SyncClipboardItem转换为ClipboardItem
    fn sync_item_to_clipboard_item(&self, sync_item: &SyncClipboardItem) -> ClipboardItem {
        ClipboardItem {
            id: sync_item.id.clone(),
            content: sync_item.content.clone(),
            timestamp: sync_item.created_at.timestamp() as u64,
            item_type: sync_item.content_type.clone(),
            size: Some(sync_item.content.len() as u64),
            file_paths: None,
            file_types: None,
        }
    }
} 