//! HLC (Hybrid Logical Clock) implementation for timestamp generation
//! 
//! This module provides a 64-bit hybrid logical clock that combines:
//! - High 48 bits: Physical timestamp (milliseconds since Unix epoch)
//! - Low 16 bits: Logical counter for ordering within the same physical time
//! 
//! The HLC ensures strict ordering and causality even in distributed systems
//! with clock skew or multiple operations within the same millisecond.

use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::sync::atomic::{AtomicU64, Ordering};
use std::time::{SystemTime, UNIX_EPOCH};

/// A 64-bit hybrid logical clock timestamp
/// 
/// Format: | 48-bit physical time (ms) | 16-bit logical counter |
/// 
/// This provides:
/// - 8.9 million years of timestamp space (2^48 ms)
/// - 65,536 logical operations per millisecond (2^16)
/// - Total ordering across all operations
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, PartialEq, Eq, PartialOrd, Ord, Hash, Serialize, Deserialize)]
pub struct HLC(pub u64);

impl HLC {
    /// Extract the physical time component (high 48 bits)
    pub fn physical_time(&self) -> u64 {
        self.0 >> 16
    }
    
    /// Extract the logical counter component (low 16 bits)
    pub fn logical_counter(&self) -> u16 {
        (self.0 & 0xFFFF) as u16
    }
    
    /// Create a new HLC from physical time and logical counter
    pub fn new(physical_ms: u64, logical: u16) -> Self {
        // Ensure physical time fits in 48 bits
        let physical = physical_ms & 0xFFFFFFFFFFFF;
        HLC((physical << 16) | (logical as u64))
    }
    
    /// Get the current system time in milliseconds since Unix epoch
    fn current_time_ms() -> u64 {
        SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .expect("Time went backwards")
            .as_millis() as u64
    }

    /// 从当前物理时间创建 HLC
    pub fn now() -> Self {
        let physical_time_ms = Utc::now().timestamp_millis() as u64;
        Self::new(physical_time_ms, 0)
    }

    /// 从字符串解析 HLC
    pub fn from_string(s: &str) -> Result<Self, String> {
        let value = s.parse::<u64>().map_err(|e| e.to_string())?;
        Ok(Self(value))
    }

    /// 转换为字符串
    pub fn to_string(&self) -> String {
        self.0.to_string()
    }

    /// 获取对应的 DateTime
    pub fn to_datetime(&self) -> DateTime<Utc> {
        DateTime::from_timestamp_millis(self.physical_time() as i64)
            .unwrap_or_else(Utc::now)
    }

    /// 比较两个 HLC，用于 LWW 决议
    pub fn happens_after(&self, other: &HLC) -> bool {
        self.0 > other.0
    }
}

impl std::fmt::Display for HLC {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "HLC({:016x}:{}:{})", self.0, self.physical_time(), self.logical_counter())
    }
}

impl From<u64> for HLC {
    fn from(value: u64) -> Self {
        HLC(value)
    }
}

impl From<HLC> for u64 {
    fn from(hlc: HLC) -> Self {
        hlc.0
    }
}

/// Thread-safe HLC generator
/// 
/// Guarantees monotonic increasing timestamps even with:
/// - Clock skew or backwards jumps
/// - Multiple concurrent operations
/// - System time adjustments
pub struct HLCGenerator {
    last_hlc: AtomicU64,
}

impl Clone for HLCGenerator {
    fn clone(&self) -> Self {
        Self {
            last_hlc: AtomicU64::new(self.last_hlc.load(Ordering::SeqCst)),
        }
    }
}

impl HLCGenerator {
    /// Create a new HLC generator
    pub fn new() -> Self {
        Self {
            last_hlc: AtomicU64::new(0),
        }
    }
    
    /// Generate the next HLC timestamp
    /// 
    /// This method:
    /// 1. Gets current physical time
    /// 2. Loads the last generated HLC
    /// 3. Ensures the new HLC is strictly greater than the last
    /// 4. Handles logical counter overflow by advancing physical time
    /// 
    /// Returns a monotonically increasing HLC timestamp.
    pub fn next(&self) -> HLC {
        let current_time = HLC::current_time_ms();
        
        loop {
            let last_value = self.last_hlc.load(Ordering::SeqCst);
            let _last_hlc = HLC(last_value);
            
            // Calculate the new HLC value
            let new_hlc_value = if current_time > (last_value >> 16) {
                // Physical time advanced, reset logical counter
                (current_time << 16) | 1
            } else {
                // Same or earlier physical time, increment logical counter
                let new_logical = ((last_value & 0xFFFF) + 1) & 0xFFFF;
                if new_logical == 0 {
                    // Logical counter overflow, advance physical time
                    ((current_time + 1) << 16) | 1
                } else {
                    (last_value & 0xFFFFFFFFFFFF0000) | new_logical
                }
            };
            
            // Atomically update if no other thread changed it
            match self.last_hlc.compare_exchange_weak(
                last_value,
                new_hlc_value,
                Ordering::SeqCst,
                Ordering::SeqCst,
            ) {
                Ok(_) => return HLC(new_hlc_value),
                Err(_) => continue, // Retry if another thread won the race
            }
        }
    }
    
    /// Update the generator with an external HLC (for sync operations)
    /// 
    /// This ensures that locally generated timestamps are always greater than
    /// any externally observed timestamp, maintaining causality.
    pub fn update(&self, external_hlc: HLC) {
        loop {
            let current_value = self.last_hlc.load(Ordering::SeqCst);
            let new_value = current_value.max(external_hlc.0);
            
            match self.last_hlc.compare_exchange_weak(
                current_value,
                new_value,
                Ordering::SeqCst,
                Ordering::SeqCst,
            ) {
                Ok(_) => break,
                Err(_) => continue,
            }
        }
    }
}

impl Default for HLCGenerator {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_hlc_creation() {
        let hlc = HLC::new(1000, 5);
        assert_eq!(hlc.physical_time(), 1000);
        assert_eq!(hlc.logical_counter(), 5);
    }
    
    #[test]
    fn test_hlc_ordering() {
        let hlc1 = HLC::new(1000, 1);
        let hlc2 = HLC::new(1000, 2);
        let hlc3 = HLC::new(1001, 1);
        
        assert!(hlc1 < hlc2);
        assert!(hlc2 < hlc3);
        assert!(hlc1 < hlc3);
    }
    
    #[test]
    fn test_generator_monotonic() {
        let gen = HLCGenerator::new();
        let hlc1 = gen.next();
        let hlc2 = gen.next();
        let hlc3 = gen.next();
        
        assert!(hlc1 < hlc2);
        assert!(hlc2 < hlc3);
    }
    
    #[test]
    fn test_generator_update() {
        let gen = HLCGenerator::new();
        let _local = gen.next();
        
        // Simulate receiving a higher timestamp from remote
        let remote = HLC::new(9999999999, 100);
        gen.update(remote);
        
        let next_local = gen.next();
        assert!(next_local > remote);
    }
} 