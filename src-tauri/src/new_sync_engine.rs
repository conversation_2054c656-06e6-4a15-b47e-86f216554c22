use anyhow::Result;
use std::sync::Arc;
use std::path::Path;
use tokio::sync::RwLock;
use opendal::Operator;

use crate::storage_engine::StorageEngine;
use crate::cloud_sync::CloudSyncEngine;
use crate::blob_manager::BlobManager;
use crate::models::{ClipboardItem, StorageStats};
use crate::hlc::HLC;

/// 新的统一同步引擎
/// 集成本地存储、云端同步和 BLOB 管理
pub struct NewSyncEngine {
    storage: Arc<StorageEngine>,
    cloud_sync: Arc<CloudSyncEngine>,
    blob_manager: Arc<BlobManager>,
    device_id: String,
    user_id: String,
}

impl NewSyncEngine {
    /// 创建新的同步引擎
    pub async fn new<P: AsRef<Path>>(
        storage_dir: P,
        cache_dir: P,
        cloud_operator: Operator,
        user_id: String,
        device_id: String,
    ) -> Result<Self> {
        // 创建存储引擎
        let db_path = storage_dir.as_ref().join("clipboard.db");
        let storage = Arc::new(StorageEngine::new(db_path, device_id.clone()).await?);

        // 创建 BLOB 管理器
        let blob_manager = Arc::new(BlobManager::new(
            cloud_operator.clone(),
            cache_dir.as_ref().to_path_buf(),
            user_id.clone(),
        ));

        // 创建云端同步引擎
        let cloud_sync = Arc::new(CloudSyncEngine::new(
            storage.as_ref().clone(), // 克隆 StorageEngine
            cloud_operator,
            user_id.clone(),
            device_id.clone(),
        ));

        Ok(Self {
            storage,
            cloud_sync,
            blob_manager,
            device_id,
            user_id,
        })
    }

    /// 初始化：从云端恢复数据
    pub async fn initialize(&self) -> Result<()> {
        tracing::info!("初始化新同步引擎...");
        
        // 从快照恢复
        self.cloud_sync.restore_from_snapshot().await?;
        
        // 执行增量同步
        self.cloud_sync.sync().await?;
        
        tracing::info!("新同步引擎初始化完成");
        Ok(())
    }

    /// 添加文本项目
    pub async fn add_text(&self, text: &str) -> Result<()> {
        let hlc = self.storage.hlc_generator.next();
        let item = self.blob_manager.process_text(text, self.device_id.clone(), hlc).await?;

        // 添加到云端同步
        self.cloud_sync.local_add(item).await?;

        Ok(())
    }

    /// 添加图片项目
    pub async fn add_image(&self, image_data: &[u8], format: &str) -> Result<()> {
        let hlc = self.storage.hlc_generator.next();
        let item = self.blob_manager.process_image(image_data, format, self.device_id.clone(), hlc).await?;

        // 添加到云端同步
        self.cloud_sync.local_add(item).await?;

        Ok(())
    }

    /// 添加文件项目
    pub async fn add_file(&self, file_path: &str) -> Result<()> {
        let hlc = self.storage.hlc_generator.next();
        let item = self.blob_manager.process_file(file_path, self.device_id.clone(), hlc).await?;

        // 添加到云端同步
        self.cloud_sync.local_add(item).await?;

        Ok(())
    }

    /// 删除剪贴板项目
    pub async fn delete_item(&self, item_id: &str) -> Result<bool> {
        self.cloud_sync.local_delete(item_id).await
    }

    /// 获取所有剪贴板项目
    pub async fn get_all_items(&self) -> Result<Vec<ClipboardItem>> {
        self.storage.get_all_items().await
    }

    /// 获取项目的完整内容
    pub async fn get_item_content(&self, item: &ClipboardItem) -> Result<Vec<u8>> {
        self.blob_manager.get_item_content(item).await
    }

    /// 执行同步
    pub async fn sync(&self) -> Result<()> {
        self.cloud_sync.sync().await
    }

    /// 创建快照
    pub async fn create_snapshot(&self) -> Result<()> {
        self.cloud_sync.create_snapshot().await
    }

    /// 清空所有数据
    pub async fn clear_all(&self) -> Result<()> {
        self.cloud_sync.clear_all().await
    }

    /// 获取存储统计信息
    pub async fn get_stats(&self) -> Result<StorageStats> {
        self.storage.get_stats().await
    }

    /// 获取同步状态
    pub async fn get_sync_status(&self) -> Result<serde_json::Value> {
        self.cloud_sync.get_status().await
    }

    /// 启动后台同步任务
    pub async fn start_background_sync(&self, interval_seconds: u64) -> Result<()> {
        let cloud_sync = self.cloud_sync.clone();
        let blob_manager = self.blob_manager.clone();
        
        tokio::spawn(async move {
            let mut sync_timer = tokio::time::interval(tokio::time::Duration::from_secs(interval_seconds));
            let mut cache_cleanup_timer = tokio::time::interval(tokio::time::Duration::from_secs(3600)); // 每小时清理缓存

            loop {
                tokio::select! {
                    _ = sync_timer.tick() => {
                        if let Err(e) = cloud_sync.sync().await {
                            tracing::error!("后台同步失败: {}", e);
                        }
                    }
                    _ = cache_cleanup_timer.tick() => {
                        if let Err(e) = blob_manager.cleanup_cache(1024 * 1024 * 1024).await { // 1GB
                            tracing::error!("缓存清理失败: {}", e);
                        }
                    }
                }
            }
        });

        Ok(())
    }

    /// 根据文件路径推断 MIME 类型
    fn guess_mime_type(&self, file_path: &str) -> String {
        let extension = std::path::Path::new(file_path)
            .extension()
            .and_then(|ext| ext.to_str())
            .unwrap_or("")
            .to_lowercase();

        match extension.as_str() {
            "txt" => "text/plain",
            "json" => "application/json",
            "xml" => "application/xml",
            "html" | "htm" => "text/html",
            "css" => "text/css",
            "js" => "application/javascript",
            "png" => "image/png",
            "jpg" | "jpeg" => "image/jpeg",
            "gif" => "image/gif",
            "webp" => "image/webp",
            "svg" => "image/svg+xml",
            "pdf" => "application/pdf",
            "zip" => "application/zip",
            "tar" => "application/x-tar",
            "gz" => "application/gzip",
            "mp4" => "video/mp4",
            "avi" => "video/x-msvideo",
            "mov" => "video/quicktime",
            "mp3" => "audio/mpeg",
            "wav" => "audio/wav",
            "flac" => "audio/flac",
            _ => "application/octet-stream",
        }.to_string()
    }
} 