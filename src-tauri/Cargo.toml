[package]
name = "clippy"
version = "0.1.0"
description = "A Tauri App"
authors = ["you"]
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[lib]
# The `_lib` suffix may seem redundant but it is necessary
# to make the lib name unique and wouldn't conflict with the bin name.
# This seems to be only an issue on Windows, see https://github.com/rust-lang/cargo/issues/8519
name = "clippy_lib"
crate-type = ["staticlib", "cdylib", "rlib"]

[build-dependencies]
tauri-build = { version = "2", features = [] }

[dependencies]
tauri = { version = "2", features = ["tray-icon"] }
tauri-plugin-opener = "2"
tauri-plugin-shell = "2"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
clipboard-rs = "0.2.4"
tokio = { version = "1.0", features = ["full"] }
base64 = "0.21"
uuid = { version = "1.0", features = ["v4", "serde"] }
infer = "0.15"
opendal = { version = "0.44", features = ["services-s3", "services-fs"] }
chrono = { version = "0.4", features = ["serde"] }
anyhow = "1.0"
tracing = "0.1"
tracing-subscriber = "0.3"
dirs = "5.0"
sqlx = { version = "0.7", features = ["runtime-tokio-rustls", "sqlite", "chrono"] }
sha2 = "0.10"
hex = "0.4"
lz4_flex = "0.11"
backon = "0.4"

[dev-dependencies]
tempfile = "3.0"

[[example]]
name = "hybrid_storage_demo"
path = "examples/hybrid_storage_demo.rs"

[[example]]
name = "hybrid_sync_demo"
path = "examples/hybrid_sync_demo.rs"

[[example]]
name = "snapshot_sync_demo"
path = "examples/snapshot_sync_demo.rs"

